#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
根据现有clusters.csv格式创建测试用CSV文件
从今天(2025/07/10)开始，生成30天的测试数据
"""

import pandas as pd
import random
from datetime import datetime, timed<PERSON><PERSON>

def create_test_data_with_dates(start_date="2025-07-10", num_days=30):
    """创建带日期的测试数据"""
    data = []
    
    # 解析开始日期
    start = datetime.strptime(start_date, "%Y-%m-%d")
    
    for i in range(num_days):
        # 计算当前日期
        current_date = start + timedelta(days=i)
        date_str = current_date.strftime("%Y/%m/%d")
        
        # 生成随机但合理的集群数据
        total_flops = random.randint(1000, 50000)
        used_flops = random.randint(0, total_flops // 2)
        available_flops = total_flops - used_flops
        
        total_cpu_cores = random.randint(64, 2048)
        used_cpu_cores = random.randint(0, total_cpu_cores // 2)
        available_cpu_cores = total_cpu_cores - used_cpu_cores
        
        total_mem_size = random.randint(512, 12000)
        used_mem_size = random.randint(0, total_mem_size // 2)
        available_mem_size = total_mem_size - used_mem_size
        
        total_gpu_cards = random.randint(8, 100)
        used_gpu_cards = random.randint(0, total_gpu_cards // 2)
        available_gpu_cards = total_gpu_cards - used_gpu_cards
        
        total_gpu_mem_size = total_gpu_cards * random.randint(40, 80)
        used_gpu_mem_size = random.randint(0, total_gpu_mem_size // 2)
        available_gpu_mem_size = total_gpu_mem_size - used_gpu_mem_size
        
        total_storage_size = random.randint(10000, 100000)
        used_storage_size = random.randint(1000, total_storage_size // 2)
        available_storage_size = total_storage_size - used_storage_size
        
        row_data = {
            "日期": date_str,
            "id": f"cluster_{i+1:03d}",
            "ping": random.randint(5, 50),
            "total_flops": total_flops,
            "used_flops": used_flops,
            "available_flops": available_flops,
            "total_cpu_cores": total_cpu_cores,
            "used_cpu_cores": used_cpu_cores,
            "available_cpu_cores": available_cpu_cores,
            "total_mem_size": total_mem_size,
            "used_mem_size": used_mem_size,
            "available_mem_size": available_mem_size,
            "total_gpu_cards": total_gpu_cards,
            "used_gpu_cards": used_gpu_cards,
            "available_gpu_cards": available_gpu_cards,
            "total_gpu_mem_size": total_gpu_mem_size,
            "used_gpu_mem_size": used_gpu_mem_size,
            "available_gpu_mem_size": available_gpu_mem_size,
            "total_storage_size": total_storage_size,
            "used_storage_size": used_storage_size,
            "available_storage_size": available_storage_size
        }
        
        data.append(row_data)
    
    return data

def main():
    """主函数"""
    print("创建测试用CSV文件...")
    
    # 生成从今天开始的30天测试数据
    test_data = create_test_data_with_dates("2025-07-10", 30)
    
    # 创建DataFrame
    df = pd.DataFrame(test_data)
    
    # 保存到CSV文件
    filename = "clusters_test.csv"
    df.to_csv(filename, index=False, encoding='utf-8')
    
    print(f"✅ 测试CSV文件已创建: {filename}")
    print(f"📊 包含 {len(test_data)} 天的数据")
    print(f"📅 日期范围: {test_data[0]['日期']} 到 {test_data[-1]['日期']}")
    
    print("\n文件包含以下列:")
    for i, col in enumerate(df.columns, 1):
        print(f"{i:2d}. {col}")
    
    print(f"\n前5行数据预览:")
    print(df.head().to_string(index=False))
    
    # 显示今天的数据
    today_str = "2025/07/10"
    today_data = df[df['日期'] == today_str]
    if not today_data.empty:
        print(f"\n🎯 今天({today_str})的数据:")
        print(f"集群ID: {today_data.iloc[0]['id']}")
        print(f"总算力: {today_data.iloc[0]['total_flops']} TFLOPS")
        print(f"总GPU卡数: {today_data.iloc[0]['total_gpu_cards']}")
    
    print(f"\n💡 提示:")
    print(f"- 测试文件已创建，包含从今天开始的30天数据")
    print(f"- 每天都有完整的集群数据")
    print(f"- 程序会根据当前日期自动选择对应的数据进行更新")
    
    return filename

if __name__ == "__main__":
    main()
