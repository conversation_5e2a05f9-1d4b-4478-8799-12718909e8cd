#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同的证书配置
"""

import requests
import json
import hmac
import hashlib
import time
import urllib3
import ssl
from config import API_CONFIG

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_api_with_cert_config(cert_config, description):
    """使用指定证书配置测试API"""
    print(f"\n--- {description} ---")
    
    # 测试数据：先用空数组确认连接，再用实际数据测试权限
    test_cases = [
        {
            "name": "空数组（确认连接）",
            "data": {"clusters": []}
        },
        {
            "name": "实际数据（测试权限）",
            "data": {"clusters": [{
                "id": "cluster_001",
                "ping": 31,
                "total_flops": 1464,
                "used_flops": 413,
                "available_flops": 1051,
                "total_cpu_cores": 363,
                "used_cpu_cores": 166,
                "available_cpu_cores": 197,
                "total_mem_size": 2625,
                "used_mem_size": 576,
                "available_mem_size": 2049,
                "total_gpu_cards": 80,
                "used_gpu_cards": 13,
                "available_gpu_cards": 67,
                "total_gpu_mem_size": 6400,
                "used_gpu_mem_size": 2570,
                "available_gpu_mem_size": 3830,
                "total_storage_size": 56956,
                "used_storage_size": 21633,
                "available_storage_size": 35323
            }]}
        }
    ]
    
    url = f"{API_CONFIG['BASE_URL']}/powerapi/v1/clusters"
    
    for test_case in test_cases:
        print(f"\n  {test_case['name']}:")
        
        try:
            body = json.dumps(test_case['data'], separators=(',', ':'))
            
            # 生成签名
            ts = int(time.time() * 1000)
            access_key = API_CONFIG['ACCESS_KEY']
            secret_key = API_CONFIG['SECRET_KEY']
            
            to_sign = f"{ts}{access_key}{body}"
            signature = hmac.new(
                secret_key.encode('utf-8'),
                to_sign.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            headers = {
                "Content-Type": "application/json",
                "ts": str(ts),
                "accessKey": access_key,
                "sign": signature
            }
            
            # 根据证书配置构建请求参数
            request_kwargs = {
                "url": url,
                "headers": headers,
                "data": body,
                "timeout": 30
            }
            
            # 添加证书配置
            if cert_config:
                request_kwargs.update(cert_config)
            
            response = requests.post(**request_kwargs)
            
            print(f"    状态码: {response.status_code}")
            print(f"    响应: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    print(f"    ✅ 成功!")
                    if test_case['name'] == "实际数据（测试权限）":
                        print(f"    🎉 找到有写入权限的证书配置!")
                        return True
                else:
                    print(f"    ❌ 错误码: {result.get('code')} - {result.get('msg')}")
            else:
                print(f"    ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ 请求失败: {e}")
    
    return False

def test_p12_certificate():
    """测试P12证书"""
    print("🔍 测试P12证书")
    
    # P12证书需要密码，尝试常见密码
    common_passwords = ["", "123456", "password", "悦科数据", "shanghaiai"]
    
    for password in common_passwords:
        print(f"\n尝试P12密码: {'(空密码)' if password == '' else password}")
        
        try:
            # 尝试加载P12证书
            import ssl
            from cryptography.hazmat.primitives import serialization
            from cryptography.hazmat.primitives.serialization import pkcs12
            
            with open("上海市算力调度平台（悦科数据）.p12", "rb") as f:
                p12_data = f.read()
            
            try:
                # 尝试解析P12文件
                private_key, certificate, additional_certificates = pkcs12.load_key_and_certificates(
                    p12_data, 
                    password.encode('utf-8') if password else None
                )
                
                print("  ✅ P12证书解析成功")
                
                # 将证书和私钥保存为临时PEM文件
                with open("temp_cert.pem", "wb") as f:
                    f.write(certificate.public_bytes(serialization.Encoding.PEM))
                
                with open("temp_key.pem", "wb") as f:
                    f.write(private_key.private_bytes(
                        encoding=serialization.Encoding.PEM,
                        format=serialization.PrivateFormat.PKCS8,
                        encryption_algorithm=serialization.NoEncryption()
                    ))
                
                # 测试使用P12证书
                cert_config = {
                    "cert": ("temp_cert.pem", "temp_key.pem"),
                    "verify": False
                }
                
                success = test_api_with_cert_config(cert_config, f"P12证书 (密码: {password or '空'})")
                
                # 清理临时文件
                import os
                try:
                    os.remove("temp_cert.pem")
                    os.remove("temp_key.pem")
                except:
                    pass
                
                if success:
                    return password
                    
            except Exception as e:
                print(f"  ❌ P12解析失败: {e}")
                
        except ImportError:
            print("  ⚠️  需要安装cryptography库来处理P12证书")
            break
        except Exception as e:
            print(f"  ❌ P12测试失败: {e}")
    
    return None

def main():
    """主函数"""
    print("🔍 测试不同证书配置的权限")
    print("=" * 60)
    
    # 测试配置列表
    cert_configs = [
        {
            "config": {"cert": ("上海市算力调度平台（悦科数据）.crt", "上海市算力调度平台（悦科数据）.key"), "verify": False},
            "desc": "当前使用的.crt + .key证书"
        },
        {
            "config": {"verify": False},
            "desc": "跳过SSL验证（无客户端证书）"
        },
        {
            "config": {"verify": True},
            "desc": "默认SSL验证"
        }
    ]
    
    successful_config = None
    
    # 测试每种配置
    for cert_info in cert_configs:
        try:
            success = test_api_with_cert_config(cert_info["config"], cert_info["desc"])
            if success:
                successful_config = cert_info
                break
        except Exception as e:
            print(f"配置测试失败: {e}")
    
    # 测试P12证书
    if not successful_config:
        print("\n" + "=" * 60)
        p12_password = test_p12_certificate()
        if p12_password is not None:
            print(f"🎉 P12证书可用，密码: {p12_password or '(空密码)'}")
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    
    if successful_config:
        print(f"✅ 找到有写入权限的证书配置: {successful_config['desc']}")
        print("\n建议:")
        print("1. 更新主程序使用这个证书配置")
        print("2. 重新发送实际数据")
    else:
        print("❌ 所有证书配置都无法发送实际数据")
        print("\n可能的原因:")
        print("1. 所有证书都只有读取权限，没有写入权限")
        print("2. 需要额外的权限申请")
        print("3. API密钥权限限制")
        print("4. 需要在平台上预先注册集群ID")
        
        print("\n建议:")
        print("1. 联系API提供方确认证书权限")
        print("2. 申请具有写入权限的证书")
        print("3. 确认API密钥的权限范围")
        print("4. 询问是否需要预先注册集群")

if __name__ == "__main__":
    main()
