@echo off
chcp 65001 >nul
echo 🔧 安装Windows服务依赖包
echo ========================

set PYTHON_EXE=C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe

echo 正在安装pywin32包...
"%PYTHON_EXE%" -m pip install pywin32

if %errorlevel% equ 0 (
    echo ✅ pywin32安装成功
    
    echo.
    echo 正在配置pywin32...
    "%PYTHON_EXE%" -c "import win32api; print('pywin32配置成功')"
    
    if %errorlevel% equ 0 (
        echo ✅ 依赖包安装完成
        echo.
        echo 💡 现在可以使用以下命令管理Windows服务:
        echo   python cluster_service.py install   - 安装服务
        echo   python cluster_service.py start     - 启动服务
        echo   python cluster_service.py stop      - 停止服务
        echo   python cluster_service.py uninstall - 卸载服务
    ) else (
        echo ❌ pywin32配置失败
    )
) else (
    echo ❌ pywin32安装失败
    echo 请检查网络连接和Python环境
)

echo.
echo 按任意键退出...
pause >nul
