#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用CSV中的真实数据测试API
"""

import requests
import json
import hmac
import hashlib
import time
import pandas as pd
import urllib3
from config import API_CONFIG, CSV_COLUMNS

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_with_real_csv_data():
    """使用CSV中的真实数据测试"""
    print("=== 使用CSV中的真实数据测试 ===")
    
    try:
        # 读取CSV文件
        df = pd.read_csv('clusters.csv')
        print(f"✅ 成功读取CSV文件，共{len(df)}行数据")
        
        # 获取今天的数据
        date_column = '日期'
        if date_column in df.columns:
            df[date_column] = pd.to_datetime(df[date_column], errors='coerce')
            today = pd.Timestamp.now().date()
            today_data = df[df[date_column].dt.date == today]
            
            if not today_data.empty:
                row = today_data.iloc[0]
                print(f"✅ 找到今天({today})的数据")
            else:
                row = df.iloc[0]
                print(f"✅ 使用第一行数据")
        else:
            row = df.iloc[0]
            print(f"✅ 使用第一行数据")
        
        # 显示原始CSV数据
        print("\n原始CSV数据:")
        for col in df.columns:
            if col in row:
                print(f"  {col}: {row[col]}")
        
        # 构建集群数据
        cluster_data = {}
        for api_field, csv_column in CSV_COLUMNS.items():
            if api_field == "id":
                cluster_data[api_field] = str(row.get(csv_column, "cluster_001"))
            elif api_field == "ping":
                cluster_data[api_field] = int(row.get(csv_column, 10))
            else:
                cluster_data[api_field] = int(row.get(csv_column, 0))
        
        print(f"\n转换后的API数据:")
        print(f"  集群ID: {cluster_data['id']}")
        print(f"  Ping: {cluster_data['ping']}ms")
        print(f"  总算力: {cluster_data['total_flops']} TFLOPS")
        print(f"  已用算力: {cluster_data['used_flops']} TFLOPS")
        print(f"  可用算力: {cluster_data['available_flops']} TFLOPS")
        print(f"  总GPU卡数: {cluster_data['total_gpu_cards']}")
        print(f"  已用GPU卡数: {cluster_data['used_gpu_cards']}")
        print(f"  可用GPU卡数: {cluster_data['available_gpu_cards']}")
        
        # 验证数据一致性
        print(f"\n数据一致性检查:")
        flops_check = cluster_data['total_flops'] == (cluster_data['used_flops'] + cluster_data['available_flops'])
        gpu_check = cluster_data['total_gpu_cards'] == (cluster_data['used_gpu_cards'] + cluster_data['available_gpu_cards'])
        cpu_check = cluster_data['total_cpu_cores'] == (cluster_data['used_cpu_cores'] + cluster_data['available_cpu_cores'])
        
        print(f"  算力一致性: {flops_check} ({cluster_data['total_flops']} = {cluster_data['used_flops']} + {cluster_data['available_flops']})")
        print(f"  GPU一致性: {gpu_check} ({cluster_data['total_gpu_cards']} = {cluster_data['used_gpu_cards']} + {cluster_data['available_gpu_cards']})")
        print(f"  CPU一致性: {cpu_check} ({cluster_data['total_cpu_cores']} = {cluster_data['used_cpu_cores']} + {cluster_data['available_cpu_cores']})")
        
        return cluster_data
        
    except Exception as e:
        print(f"❌ 读取CSV数据失败: {e}")
        return None

def test_api_call(cluster_data):
    """测试API调用"""
    print(f"\n=== 测试API调用 ===")
    
    # 构建API请求
    api_data = {"clusters": [cluster_data]}
    body = json.dumps(api_data, separators=(',', ':'))
    
    # 生成签名
    ts = int(time.time() * 1000)
    access_key = API_CONFIG['ACCESS_KEY']
    secret_key = API_CONFIG['SECRET_KEY']
    
    to_sign = f"{ts}{access_key}{body}"
    signature = hmac.new(
        secret_key.encode('utf-8'),
        to_sign.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    headers = {
        "Content-Type": "application/json",
        "ts": str(ts),
        "accessKey": access_key,
        "sign": signature
    }
    
    url = f"{API_CONFIG['BASE_URL']}/powerapi/v1/clusters"
    cert_file = "上海市算力调度平台（悦科数据）.crt"
    key_file = "上海市算力调度平台（悦科数据）.key"
    
    print(f"请求URL: {url}")
    print(f"请求数据: {body}")
    print(f"数据大小: {len(body)} 字节")
    
    try:
        response = requests.post(
            url,
            headers=headers,
            data=body,
            timeout=30,
            cert=(cert_file, key_file),
            verify=False
        )
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("🎉 CSV数据API调用成功!")
                print("✅ 对方应该已经收到完整的集群数据!")
                return True
            else:
                print(f"❌ API返回错误: {result}")
                
                # 分析具体错误
                error_code = result.get("code")
                error_msg = result.get("msg")
                
                if error_code == 400:
                    print("可能的原因:")
                    print("1. 数据格式不符合要求")
                    print("2. 数值计算不一致")
                    print("3. 字段类型错误")
                elif error_code == 20011:
                    print("数据类型错误 - 检查是否有字符串数值")
                elif error_code == 20012:
                    print("价格单位错误 - 但这是集群API，不应该有这个错误")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    return False

def main():
    """主函数"""
    print("🔍 使用CSV真实数据测试API")
    print("=" * 60)
    
    # 读取CSV数据
    cluster_data = test_with_real_csv_data()
    
    if not cluster_data:
        print("❌ 无法读取CSV数据，测试终止")
        return
    
    # 测试API调用
    success = test_api_call(cluster_data)
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 CSV真实数据发送成功!")
        print("✅ 对方已收到完整的集群数据")
        print("\n建议:")
        print("1. 立即运行主程序发送今天的数据")
        print("2. 程序会自动每日更新")
    else:
        print("❌ CSV真实数据发送失败")
        print("\n可能需要:")
        print("1. 检查CSV数据格式")
        print("2. 确认数值计算一致性")
        print("3. 验证所有字段都是整数类型")
        print("4. 联系API提供方确认具体要求")

if __name__ == "__main__":
    main()
