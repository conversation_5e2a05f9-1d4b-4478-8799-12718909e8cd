#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试发送真实数据
"""

import requests
import json
import hmac
import hashlib
import time
import pandas as pd
import urllib3
from config import API_CONFIG, CSV_COLUMNS

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def get_today_data():
    """获取今天的数据"""
    try:
        df = pd.read_csv('clusters.csv')
        
        # 获取今天的数据
        date_column = '日期'
        if date_column in df.columns:
            df[date_column] = pd.to_datetime(df[date_column], errors='coerce')
            today = pd.Timestamp.now().date()
            today_data = df[df[date_column].dt.date == today]
            
            if not today_data.empty:
                row = today_data.iloc[0]
                print(f"✅ 找到今天({today})的数据")
            else:
                row = df.iloc[0]
                print(f"⚠️  使用第一行数据")
        else:
            row = df.iloc[0]
            print(f"⚠️  使用第一行数据")
        
        # 构建集群数据
        cluster_data = {}
        for api_field, csv_column in CSV_COLUMNS.items():
            if api_field == "id":
                cluster_data[api_field] = str(row.get(csv_column, "test_cluster"))
            elif api_field == "ping":
                cluster_data[api_field] = int(row.get(csv_column, 10))
            else:
                cluster_data[api_field] = int(row.get(csv_column, 0))
        
        print(f"集群数据:")
        print(f"  ID: {cluster_data['id']}")
        print(f"  总算力: {cluster_data['total_flops']} TFLOPS")
        print(f"  已用算力: {cluster_data['used_flops']} TFLOPS")
        print(f"  可用算力: {cluster_data['available_flops']} TFLOPS")
        print(f"  总GPU卡数: {cluster_data['total_gpu_cards']}")
        print(f"  已用GPU卡数: {cluster_data['used_gpu_cards']}")
        print(f"  可用GPU卡数: {cluster_data['available_gpu_cards']}")
        
        return cluster_data
        
    except Exception as e:
        print(f"❌ 数据读取失败: {e}")
        return None

def test_send_real_data(cluster_data):
    """测试发送真实数据"""
    print(f"\n=== 测试发送真实数据 ===")
    
    # 构建API请求
    api_data = {"clusters": [cluster_data]}
    body = json.dumps(api_data, separators=(',', ':'))
    
    # 生成签名
    ts = int(time.time() * 1000)
    access_key = API_CONFIG['ACCESS_KEY']
    secret_key = API_CONFIG['SECRET_KEY']
    
    to_sign = f"{ts}{access_key}{body}"
    signature = hmac.new(
        secret_key.encode('utf-8'),
        to_sign.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    headers = {
        "Content-Type": "application/json",
        "ts": str(ts),
        "accessKey": access_key,
        "sign": signature
    }
    
    url = f"{API_CONFIG['BASE_URL']}/powerapi/v1/clusters"
    cert_file = "上海市算力调度平台（悦科数据）.crt"
    key_file = "上海市算力调度平台（悦科数据）.key"
    
    print(f"请求URL: {url}")
    print(f"数据大小: {len(body)} 字节")
    print(f"请求数据: {body}")
    
    try:
        response = requests.post(
            url,
            headers=headers,
            data=body,
            timeout=30,
            cert=(cert_file, key_file),
            verify=False
        )
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("🎉 真实数据发送成功!")
                return True
            elif result.get("code") == 400:
                print("❌ 400错误 - 请求参数错误")
                print("可能的原因:")
                print("1. 数据格式不符合API要求")
                print("2. 数值范围超出限制")
                print("3. 必需字段缺失")
                print("4. 数据类型不正确")
            else:
                print(f"❌ API返回错误: {result}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    return False

def test_empty_vs_real():
    """对比测试空数组和真实数据"""
    print(f"\n=== 对比测试：空数组 vs 真实数据 ===")
    
    cert_file = "上海市算力调度平台（悦科数据）.crt"
    key_file = "上海市算力调度平台（悦科数据）.key"
    url = f"{API_CONFIG['BASE_URL']}/powerapi/v1/clusters"
    
    # 测试1: 空数组
    print("--- 测试1: 空数组 ---")
    empty_data = {"clusters": []}
    body = json.dumps(empty_data, separators=(',', ':'))
    
    ts = int(time.time() * 1000)
    access_key = API_CONFIG['ACCESS_KEY']
    secret_key = API_CONFIG['SECRET_KEY']
    
    to_sign = f"{ts}{access_key}{body}"
    signature = hmac.new(
        secret_key.encode('utf-8'),
        to_sign.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    headers = {
        "Content-Type": "application/json",
        "ts": str(ts),
        "accessKey": access_key,
        "sign": signature
    }
    
    try:
        response = requests.post(url, headers=headers, data=body, timeout=30, cert=(cert_file, key_file), verify=False)
        print(f"空数组响应: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"空数组请求失败: {e}")
    
    # 测试2: 真实数据
    print("\n--- 测试2: 真实数据 ---")
    cluster_data = get_today_data()
    if cluster_data:
        success = test_send_real_data(cluster_data)
        return success
    
    return False

def main():
    """主函数"""
    print("🔍 测试发送真实集群数据")
    print("=" * 60)
    
    # 获取今天的数据
    cluster_data = get_today_data()
    
    if not cluster_data:
        print("❌ 无法获取数据，测试终止")
        return
    
    # 对比测试
    success = test_empty_vs_real()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 真实数据发送成功!")
        print("现在对方应该能收到完整的集群数据了。")
    else:
        print("❌ 真实数据发送失败")
        print("\n可能需要:")
        print("1. 联系API提供方确认数据格式要求")
        print("2. 检查数据验证规则")
        print("3. 确认API权限设置")
        print("\n建议暂时保持发送空数组，直到确认正确的数据格式")

if __name__ == "__main__":
    main()
