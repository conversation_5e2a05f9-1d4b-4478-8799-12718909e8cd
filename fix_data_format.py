#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复数据格式问题
"""

import requests
import json
import hmac
import hashlib
import time
import urllib3
from config import API_CONFIG

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_different_data_formats():
    """测试不同的数据格式"""
    print("🔧 测试不同数据格式以修复400错误")
    
    # 基础集群数据
    base_cluster = {
        "id": "cluster_001",
        "ping": 31,
        "total_flops": 1464,
        "used_flops": 413,
        "available_flops": 1051,
        "total_cpu_cores": 363,
        "used_cpu_cores": 166,
        "available_cpu_cores": 197,
        "total_mem_size": 2625,
        "used_mem_size": 576,
        "available_mem_size": 2049,
        "total_gpu_cards": 80,
        "used_gpu_cards": 13,
        "available_gpu_cards": 67,
        "total_gpu_mem_size": 6400,
        "used_gpu_mem_size": 2570,
        "available_gpu_mem_size": 3830,
        "total_storage_size": 56956,
        "used_storage_size": 21633,
        "available_storage_size": 35323
    }
    
    # 测试不同的数据格式
    test_formats = [
        {
            "name": "标准格式（clusters数组）",
            "data": {"clusters": [base_cluster]}
        },
        {
            "name": "单个集群对象",
            "data": base_cluster
        },
        {
            "name": "简化字段",
            "data": {
                "clusters": [{
                    "id": "cluster_001",
                    "ping": 31,
                    "total_flops": 1464,
                    "used_flops": 413,
                    "available_flops": 1051
                }]
            }
        },
        {
            "name": "字符串类型数值",
            "data": {
                "clusters": [{
                    "id": "cluster_001",
                    "ping": "31",
                    "total_flops": "1464",
                    "used_flops": "413",
                    "available_flops": "1051",
                    "total_cpu_cores": "363",
                    "used_cpu_cores": "166",
                    "available_cpu_cores": "197",
                    "total_mem_size": "2625",
                    "used_mem_size": "576",
                    "available_mem_size": "2049",
                    "total_gpu_cards": "80",
                    "used_gpu_cards": "13",
                    "available_gpu_cards": "67",
                    "total_gpu_mem_size": "6400",
                    "used_gpu_mem_size": "2570",
                    "available_gpu_mem_size": "3830",
                    "total_storage_size": "56956",
                    "used_storage_size": "21633",
                    "available_storage_size": "35323"
                }]
            }
        },
        {
            "name": "最小必需字段",
            "data": {
                "clusters": [{
                    "id": "cluster_001",
                    "ping": 31
                }]
            }
        }
    ]
    
    cert_file = "上海市算力调度平台（悦科数据）.crt"
    key_file = "上海市算力调度平台（悦科数据）.key"
    url = f"{API_CONFIG['BASE_URL']}/powerapi/v1/clusters"
    
    for test_format in test_formats:
        print(f"\n--- {test_format['name']} ---")
        
        try:
            # 序列化数据
            body = json.dumps(test_format['data'], separators=(',', ':'))
            
            # 生成签名
            ts = int(time.time() * 1000)
            access_key = API_CONFIG['ACCESS_KEY']
            secret_key = API_CONFIG['SECRET_KEY']
            
            to_sign = f"{ts}{access_key}{body}"
            signature = hmac.new(
                secret_key.encode('utf-8'),
                to_sign.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            headers = {
                "Content-Type": "application/json",
                "ts": str(ts),
                "accessKey": access_key,
                "sign": signature
            }
            
            print(f"数据大小: {len(body)} 字节")
            print(f"数据内容: {body[:100]}...")
            
            # 发送请求
            response = requests.post(
                url,
                headers=headers,
                data=body,
                timeout=30,
                cert=(cert_file, key_file),
                verify=False
            )
            
            print(f"状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get("code") == 200:
                        print("🎉 数据格式正确！API调用成功!")
                        return test_format['data']
                    else:
                        print(f"API返回错误: {result}")
                except json.JSONDecodeError:
                    print("响应不是有效的JSON")
            elif response.status_code == 400:
                print("❌ 仍然是400错误，数据格式不正确")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
    
    return None

def test_api_documentation_format():
    """根据API文档测试标准格式"""
    print("\n=== 根据API文档测试标准格式 ===")
    
    # 根据API文档的标准格式
    standard_data = {
        "clusters": [
            {
                "id": "cluster_001",
                "ping": 31,
                "total_flops": 1464,
                "used_flops": 413,
                "available_flops": 1051,
                "total_cpu_cores": 363,
                "used_cpu_cores": 166,
                "available_cpu_cores": 197,
                "total_mem_size": 2625,
                "used_mem_size": 576,
                "available_mem_size": 2049,
                "total_gpu_cards": 80,
                "used_gpu_cards": 13,
                "available_gpu_cards": 67,
                "total_gpu_mem_size": 6400,
                "used_gpu_mem_size": 2570,
                "available_gpu_mem_size": 3830,
                "total_storage_size": 56956,
                "used_storage_size": 21633,
                "available_storage_size": 35323
            }
        ]
    }
    
    # 确保所有数值都是整数类型
    for cluster in standard_data["clusters"]:
        for key, value in cluster.items():
            if key != "id" and isinstance(value, (int, float, str)):
                try:
                    cluster[key] = int(float(value))
                except:
                    pass
    
    body = json.dumps(standard_data, separators=(',', ':'))
    
    # 生成签名
    ts = int(time.time() * 1000)
    access_key = API_CONFIG['ACCESS_KEY']
    secret_key = API_CONFIG['SECRET_KEY']
    
    to_sign = f"{ts}{access_key}{body}"
    signature = hmac.new(
        secret_key.encode('utf-8'),
        to_sign.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    headers = {
        "Content-Type": "application/json",
        "ts": str(ts),
        "accessKey": access_key,
        "sign": signature
    }
    
    url = f"{API_CONFIG['BASE_URL']}/powerapi/v1/clusters"
    cert_file = "上海市算力调度平台（悦科数据）.crt"
    key_file = "上海市算力调度平台（悦科数据）.key"
    
    print(f"标准格式数据: {body}")
    
    try:
        response = requests.post(
            url,
            headers=headers,
            data=body,
            timeout=30,
            cert=(cert_file, key_file),
            verify=False
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("🎉 标准格式API调用成功!")
                return True
            else:
                print(f"API返回错误: {result}")
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    return False

def main():
    """主函数"""
    print("🔧 修复400错误 - 数据格式测试")
    print("=" * 60)
    
    # 测试不同数据格式
    correct_format = test_different_data_formats()
    
    # 测试标准格式
    standard_success = test_api_documentation_format()
    
    print("\n" + "=" * 60)
    if correct_format or standard_success:
        print("🎉 找到正确的数据格式!")
        print("✅ 证书认证成功，API可以正常调用。")
        print("\n下一步:")
        print("1. 更新主程序使用正确的数据格式")
        print("2. 运行完整的数据上传测试")
    else:
        print("❌ 仍然无法找到正确的数据格式")
        print("\n建议:")
        print("1. 检查API文档中的数据格式要求")
        print("2. 联系API提供方确认数据结构")
        print("3. 检查是否有必需字段缺失")

if __name__ == "__main__":
    main()
