#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同的ID格式
"""

import requests
import json
import hmac
import hashlib
import time
import urllib3
from config import API_CONFIG

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_api_call(data, description):
    """通用API调用测试"""
    body = json.dumps(data, separators=(',', ':'))
    
    # 生成签名
    ts = int(time.time() * 1000)
    access_key = API_CONFIG['ACCESS_KEY']
    secret_key = API_CONFIG['SECRET_KEY']
    
    to_sign = f"{ts}{access_key}{body}"
    signature = hmac.new(
        secret_key.encode('utf-8'),
        to_sign.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    headers = {
        "Content-Type": "application/json",
        "ts": str(ts),
        "accessKey": access_key,
        "sign": signature
    }
    
    url = f"{API_CONFIG['BASE_URL']}/powerapi/v1/clusters"
    cert_file = "上海市算力调度平台（悦科数据）.crt"
    key_file = "上海市算力调度平台（悦科数据）.key"
    
    print(f"--- {description} ---")
    print(f"数据: {body}")
    
    try:
        response = requests.post(
            url,
            headers=headers,
            data=body,
            timeout=30,
            cert=(cert_file, key_file),
            verify=False
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("🎉 成功!")
                return True
            else:
                print(f"❌ 错误: {result}")
        
        return False
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_different_id_formats():
    """测试不同的ID格式"""
    print("=== 测试不同的ID格式 ===")
    
    # 基础数据模板（使用CSV中的真实数据）
    base_data = {
        "ping": 31,
        "total_flops": 1464,
        "used_flops": 413,
        "available_flops": 1051,
        "total_cpu_cores": 363,
        "used_cpu_cores": 166,
        "available_cpu_cores": 197,
        "total_mem_size": 2625,
        "used_mem_size": 576,
        "available_mem_size": 2049,
        "total_gpu_cards": 80,
        "used_gpu_cards": 13,
        "available_gpu_cards": 67,
        "total_gpu_mem_size": 6400,
        "used_gpu_mem_size": 2570,
        "available_gpu_mem_size": 3830,
        "total_storage_size": 56956,
        "used_storage_size": 21633,
        "available_storage_size": 35323
    }
    
    # 测试不同的ID格式
    id_tests = [
        ("纯数字ID", "1"),
        ("短数字ID", "123"),
        ("长数字ID", "12345"),
        ("API文档示例ID", "23456"),
        ("原始ID", "cluster_001"),
        ("简化ID", "cluster1"),
        ("数字字母组合", "c001"),
        ("纯字母", "test")
    ]
    
    for desc, test_id in id_tests:
        cluster_data = base_data.copy()
        cluster_data["id"] = test_id
        
        api_data = {"clusters": [cluster_data]}
        success = test_api_call(api_data, f"{desc}: {test_id}")
        print()
        
        if success:
            print(f"🎉 找到可用的ID格式: {test_id}")
            return test_id
    
    return None

def test_simplified_data():
    """测试简化的数据结构"""
    print("=== 测试简化的数据结构 ===")
    
    # 测试只包含必需字段的数据
    simplified_tests = [
        {
            "desc": "最小必需字段",
            "data": {
                "id": "1",
                "ping": 31,
                "total_flops": 1464,
                "used_flops": 413,
                "available_flops": 1051,
                "total_cpu_cores": 363,
                "used_cpu_cores": 166,
                "available_cpu_cores": 197,
                "total_mem_size": 2625,
                "used_mem_size": 576,
                "available_mem_size": 2049,
                "total_gpu_cards": 80,
                "used_gpu_cards": 13,
                "available_gpu_cards": 67,
                "total_gpu_mem_size": 6400,
                "used_gpu_mem_size": 2570,
                "available_gpu_mem_size": 3830,
                "total_storage_size": 56956,
                "used_storage_size": 21633,
                "available_storage_size": 35323
            }
        },
        {
            "desc": "使用API文档示例数值",
            "data": {
                "id": "1",
                "ping": 10,
                "total_flops": 29600,
                "used_flops": 0,
                "available_flops": 29600,
                "total_cpu_cores": 2038,
                "used_cpu_cores": 0,
                "available_cpu_cores": 2038,
                "total_mem_size": 12000,
                "used_mem_size": 0,
                "available_mem_size": 12000,
                "total_gpu_cards": 80,
                "used_gpu_cards": 0,
                "available_gpu_cards": 80,
                "total_gpu_mem_size": 6000,
                "used_gpu_mem_size": 0,
                "available_gpu_mem_size": 6000,
                "total_storage_size": 50000,
                "used_storage_size": 10000,
                "available_storage_size": 40000
            }
        },
        {
            "desc": "小数值测试",
            "data": {
                "id": "1",
                "ping": 1,
                "total_flops": 10,
                "used_flops": 1,
                "available_flops": 9,
                "total_cpu_cores": 10,
                "used_cpu_cores": 1,
                "available_cpu_cores": 9,
                "total_mem_size": 10,
                "used_mem_size": 1,
                "available_mem_size": 9,
                "total_gpu_cards": 1,
                "used_gpu_cards": 0,
                "available_gpu_cards": 1,
                "total_gpu_mem_size": 10,
                "used_gpu_mem_size": 1,
                "available_gpu_mem_size": 9,
                "total_storage_size": 100,
                "used_storage_size": 10,
                "available_storage_size": 90
            }
        }
    ]
    
    for test in simplified_tests:
        api_data = {"clusters": [test["data"]]}
        success = test_api_call(api_data, test["desc"])
        print()
        
        if success:
            print(f"🎉 找到可用的数据格式: {test['desc']}")
            return test["data"]
    
    return None

def main():
    """主函数"""
    print("🔍 测试不同的ID格式和数据结构")
    print("=" * 60)
    
    # 测试不同的ID格式
    valid_id = test_different_id_formats()
    
    if not valid_id:
        # 测试简化的数据结构
        valid_data = test_simplified_data()
    
    print("=" * 60)
    if valid_id:
        print(f"🎉 找到可用的ID格式: {valid_id}")
        print("建议:")
        print(f"1. 将CSV中的ID改为: {valid_id}")
        print("2. 重新运行主程序")
    elif valid_data:
        print("🎉 找到可用的数据格式!")
        print("建议:")
        print("1. 使用找到的数据格式更新程序")
        print("2. 调整CSV数据以匹配要求")
    else:
        print("❌ 未找到可用的格式")
        print("建议:")
        print("1. 联系API提供方确认具体要求")
        print("2. 可能需要特殊的权限或配置")

if __name__ == "__main__":
    main()
