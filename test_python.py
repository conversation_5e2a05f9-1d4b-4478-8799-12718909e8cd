print("Python is working!")
import sys
print(f"Python version: {sys.version}")
print(f"Python executable: {sys.executable}")

# 测试导入基本模块
try:
    import json
    print("✅ json module available")
except ImportError:
    print("❌ json module not available")

try:
    import os
    print("✅ os module available")
except ImportError:
    print("❌ os module not available")

# 测试导入项目需要的模块
modules_to_test = ['pandas', 'requests', 'schedule', 'openpyxl']
for module in modules_to_test:
    try:
        __import__(module)
        print(f"✅ {module} module available")
    except ImportError:
        print(f"❌ {module} module not available - needs installation")

print("\nTest completed!")
