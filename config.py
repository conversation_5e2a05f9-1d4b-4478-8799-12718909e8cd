# -*- coding: utf-8 -*-
"""
配置文件
"""

# API配置
API_CONFIG = {
    "ACCESS_KEY": "KMgUamC48v2F34MZvSBHJfGQ",
    "SECRET_KEY": "ByKU7q2ZaYC275kiHRc626pG9qNdXKUqy3k1xaA0bI9kN6pk",
    "BASE_URL": "https://api.shanghaiai.com"
}

# 文件配置
FILE_CONFIG = {
    "DATA_FILE": "clusters.csv",  # 数据文件（CSV格式）
    "LOG_FILE": "cluster_updater.log"
}

# 调度配置
SCHEDULE_CONFIG = {
    "UPDATE_TIME": "09:00",  # 每日更新时间
    "CHECK_INTERVAL": 60     # 检查间隔（秒）
}

# CSV列名映射（根据实际CSV文件调整）
CSV_COLUMNS = {
    "id": "id",
    "ping": "ping", 
    "total_flops": "total_flops",
    "used_flops": "used_flops",
    "available_flops": "available_flops",
    "total_cpu_cores": "total_cpu_cores",
    "used_cpu_cores": "used_cpu_cores", 
    "available_cpu_cores": "available_cpu_cores",
    "total_mem_size": "total_mem_size",
    "used_mem_size": "used_mem_size",
    "available_mem_size": "available_mem_size",
    "total_gpu_cards": "total_gpu_cards",
    "used_gpu_cards": "used_gpu_cards",
    "available_gpu_cards": "available_gpu_cards",
    "total_gpu_mem_size": "total_gpu_mem_size",
    "used_gpu_mem_size": "used_gpu_mem_size",
    "available_gpu_mem_size": "available_gpu_mem_size",
    "total_storage_size": "total_storage_size",
    "used_storage_size": "used_storage_size",
    "available_storage_size": "available_storage_size"
}
