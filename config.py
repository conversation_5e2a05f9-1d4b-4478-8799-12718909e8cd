# -*- coding: utf-8 -*-
"""
配置文件
"""

# API配置
API_CONFIG = {
    "ACCESS_KEY": "KMgUamC48v2F34MZvSBHJfGQ",
    "SECRET_KEY": "ByKU7q2ZaYC275kiHRc626pG9qNdXKUqy3k1xaA0bI9kN6pk",
    "BASE_URL": "https://api.shanghaiai.com"
}

# 文件配置
FILE_CONFIG = {
    "DATA_FILE": "clusters.csv",  # 数据文件（CSV格式）
    "LOG_FILE": "cluster_updater.log"
}

# 调度配置
SCHEDULE_CONFIG = {
    "UPDATE_TIME": "09:00",  # 每日更新时间
    "CHECK_INTERVAL": 60     # 检查间隔（秒）
}

# CSV列名映射（根据实际CSV文件调整）
CSV_COLUMNS = {
    "id": " 数据中心集群 id ",
    "ping": " ping值，代表机房的访问速度，用于前端展现。目前对该字段要求不严格。 ",
    "total_flops": " 该数据中心集群接入的GPU总算力，单位 TFLOPS（FP16非稀疏） ",
    "used_flops": " 该数据中心集群接入的GPU已使用算力，单位 TFLOPS（FP16非稀疏） ",
    "available_flops": " 该数据中心集群接入的GPU空闲算力，单位 TFLOPS（FP16非稀疏） ",
    "total_cpu_cores": " 该数据中心集群接入的总cpu核数 ",
    "used_cpu_cores": " 该数据中心集群接入的已使用cpu核数 ",
    "available_cpu_cores": " 该数据中心集群接入的空闲cpu核数 ",
    "total_mem_size": " 该数据中心集群接入的总内存，单位GB ",
    "used_mem_size": " 该数据中心集群接入的已使用内存，单位GB ",
    "available_mem_size": " 该数据中心集群接入的空闲内存，单位GB ",
    "total_gpu_cards": " 该数据中心集群接入的总卡数，单位张 ",
    "used_gpu_cards": " 该数据中心集群接入的已使用卡数，单位张 ",
    "available_gpu_cards": " 该数据中心集群接入的空闲卡数，单位张 ",
    "total_gpu_mem_size": " 该数据中心集群接入的总显卡内存，单位GB ",
    "used_gpu_mem_size": " 该数据中心集群接入的已使用显卡内存，单位GB ",
    "available_gpu_mem_size": " 该数据中心集群接入的空闲显卡内存，单位GB ",
    "total_storage_size": " 该数据中心集群接入的总存储容量，单位GB ",
    "used_storage_size": " 该数据中心集群接入的已使用存储容量，单位GB ",
    "available_storage_size": " 该数据中心集群接入的空闲存储容量，单位GB "
}
