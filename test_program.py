#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化测试程序
"""

import pandas as pd
import requests
import json
import hmac
import hashlib
import time
import logging
from config import API_CONFIG, FILE_CONFIG
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_excel_loading():
    """测试Excel文件加载"""
    print("=== 测试Excel文件加载 ===")
    try:
        df = pd.read_excel(FILE_CONFIG['EXCEL_FILE'])
        print(f"✅ 成功加载Excel文件，共{len(df)}行数据")
        print(f"列名: {list(df.columns)}")
        print(f"前3行数据:")
        print(df.head(3))
        return df
    except Exception as e:
        print(f"❌ Excel文件加载失败: {e}")
        return None

def test_data_processing(df):
    """测试数据处理"""
    print("\n=== 测试数据处理 ===")
    if df is None:
        print("❌ 没有数据可处理")
        return None
    
    try:
        # 获取第一行数据
        row = df.iloc[0]
        
        # 构建集群数据
        cluster_data = {
            "id": str(row.get("id", "test_cluster")),
            "ping": int(row.get("ping", 10)),
            "total_flops": int(row.get("total_flops", 0)),
            "used_flops": int(row.get("used_flops", 0)),
            "available_flops": int(row.get("available_flops", 0)),
            "total_cpu_cores": int(row.get("total_cpu_cores", 0)),
            "used_cpu_cores": int(row.get("used_cpu_cores", 0)),
            "available_cpu_cores": int(row.get("available_cpu_cores", 0)),
            "total_mem_size": int(row.get("total_mem_size", 0)),
            "used_mem_size": int(row.get("used_mem_size", 0)),
            "available_mem_size": int(row.get("available_mem_size", 0)),
            "total_gpu_cards": int(row.get("total_gpu_cards", 0)),
            "used_gpu_cards": int(row.get("used_gpu_cards", 0)),
            "available_gpu_cards": int(row.get("available_gpu_cards", 0)),
            "total_gpu_mem_size": int(row.get("total_gpu_mem_size", 0)),
            "used_gpu_mem_size": int(row.get("used_gpu_mem_size", 0)),
            "available_gpu_mem_size": int(row.get("available_gpu_mem_size", 0)),
            "total_storage_size": int(row.get("total_storage_size", 0)),
            "used_storage_size": int(row.get("used_storage_size", 0)),
            "available_storage_size": int(row.get("available_storage_size", 0))
        }
        
        print(f"✅ 数据处理成功")
        print(f"集群ID: {cluster_data['id']}")
        print(f"总算力: {cluster_data['total_flops']} TFLOPS")
        print(f"总GPU卡数: {cluster_data['total_gpu_cards']}")
        
        return cluster_data
    except Exception as e:
        print(f"❌ 数据处理失败: {e}")
        return None

def test_api_call(cluster_data):
    """测试API调用"""
    print("\n=== 测试API调用 ===")
    if cluster_data is None:
        print("❌ 没有数据可发送")
        return False
    
    try:
        access_key = API_CONFIG['ACCESS_KEY']
        secret_key = API_CONFIG['SECRET_KEY']
        base_url = API_CONFIG['BASE_URL']
        
        # 构建请求数据
        payload = {"clusters": [cluster_data]}
        body = json.dumps(payload, ensure_ascii=False)
        
        # 生成签名
        ts = int(time.time() * 1000)
        to_sign = f"{ts}{access_key}{body}"
        signature = hmac.new(
            secret_key.encode('utf-8'),
            to_sign.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        # 构建请求头
        headers = {
            "Content-Type": "application/json",
            "ts": str(ts),
            "accessKey": access_key,
            "sign": signature
        }
        
        print(f"请求URL: {base_url}/powerapi/v1/clusters")
        print(f"请求数据大小: {len(body)} 字节")
        
        # 发送请求
        url = f"{base_url}/powerapi/v1/clusters"
        response = requests.post(url, headers=headers, data=body, timeout=30, verify=False)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ API调用成功!")
                return True
            else:
                print(f"❌ API返回错误: {result}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        return False

def main():
    """主函数"""
    print("TaoPower API 程序测试")
    print("=" * 50)
    
    # 测试Excel加载
    df = test_excel_loading()
    
    # 测试数据处理
    cluster_data = test_data_processing(df)
    
    # 测试API调用
    api_success = test_api_call(cluster_data)
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"Excel加载: {'✅ 成功' if df is not None else '❌ 失败'}")
    print(f"数据处理: {'✅ 成功' if cluster_data is not None else '❌ 失败'}")
    print(f"API调用: {'✅ 成功' if api_success else '❌ 失败'}")
    
    if df is not None and cluster_data is not None:
        print("\n🎉 程序核心功能正常！")
        if not api_success:
            print("⚠️  API连接有问题，可能是网络或服务器问题")
    else:
        print("\n❌ 程序有基础问题需要解决")

if __name__ == "__main__":
    main()
