# TaoPower API 集群数据自动更新程序

## 🎯 功能特性

✅ **智能日期匹配**：根据CSV文件中的日期列，自动选择当天对应的数据进行更新
✅ **自动定时更新**：每天定时（默认上午9:00）自动执行数据更新
✅ **完整API支持**：支持TaoPower API的所有20个集群数据字段
✅ **安全认证**：使用HMAC-SHA256签名算法确保API调用安全
✅ **智能容错**：当天无数据时自动选择最接近日期的数据
✅ **详细日志**：完整记录程序运行状态和API调用结果

## 🚀 快速开始

### 1. 运行程序
```bash
双击运行 start.bat
```

### 2. 数据文件
程序使用 `clusters.csv` 作为数据源：
- 📅 包含按日期排列的集群数据
- 📊 每天一条完整的集群数据记录
- 🔄 程序根据当前日期自动选择对应数据

### 3. 工作原理
```
当前日期 → 查找CSV中对应日期 → 提取集群数据 → 发送到API
```

## 📊 CSV文件格式

### 必需列名：
- **日期**：日期列（格式：2025/07/10）
- **id**：集群ID
- **ping**：网络延迟
- **算力相关**：total_flops, used_flops, available_flops
- **CPU相关**：total_cpu_cores, used_cpu_cores, available_cpu_cores
- **内存相关**：total_mem_size, used_mem_size, available_mem_size
- **GPU相关**：total_gpu_cards, used_gpu_cards, available_gpu_cards
- **GPU内存**：total_gpu_mem_size, used_gpu_mem_size, available_gpu_mem_size
- **存储相关**：total_storage_size, used_storage_size, available_storage_size

### 示例数据：
```csv
日期,id,ping,total_flops,used_flops,available_flops,...
2025/07/10,cluster_001,31,1464,413,1051,...
2025/07/11,cluster_002,14,39740,14849,24891,...
```

## ⚙️ 配置说明

在 `config.py` 文件中可以修改：

```python
# 更新时间
SCHEDULE_CONFIG = {
    "UPDATE_TIME": "09:00",  # 每日更新时间
    "CHECK_INTERVAL": 60     # 检查间隔（秒）
}

# 数据文件
FILE_CONFIG = {
    "DATA_FILE": "clusters.csv",  # CSV文件路径
    "LOG_FILE": "cluster_updater.log"  # 日志文件路径
}
```

## 🧪 运行结果示例

当程序运行时，会根据当前日期自动选择对应的数据：

```
✅ 找到今天的数据，共1行
📊 构建数据成功，集群ID: cluster_001
🔄 准备更新1条集群数据到API
```

## 📝 日志监控

程序运行日志保存在 `cluster_updater.log` 文件中：
```
2025-07-10 15:23:35,411 - INFO - 发现日期列: 日期
2025-07-10 15:23:35,417 - INFO - 找到今天(2025-07-10)的数据，共1行
2025-07-10 15:23:35,418 - INFO - 构建数据成功，集群ID: cluster_001
2025-07-10 15:23:35,418 - INFO - 准备更新1条集群数据
```

## 🔧 故障排除

1. **程序无法启动**：检查Python环境和依赖包
2. **CSV文件读取失败**：检查文件格式和列名是否正确
3. **未找到日期数据**：检查CSV中的日期格式和当前日期
4. **API调用失败**：检查网络连接和API密钥

## 📁 项目文件

### 项目文件：
- `cluster_data_updater.py` - 主程序（支持日期匹配）
- `config.py` - 配置文件
- `clusters.csv` - 集群数据文件
- `start.bat` - 启动脚本
- `requirements.txt` - Python依赖包列表
- `README.md` - 使用说明文档

## 🎯 使用场景

1. **每日定时更新**：程序每天上午9:00自动运行，根据当天日期更新对应数据
2. **手动测试**：运行演示脚本查看不同日期的数据更新效果
3. **数据验证**：通过日志文件监控程序运行状态和API调用结果

## 📈 下一步

1. 将您的实际集群数据整理成CSV格式
2. 确保包含日期列和所有必需字段
3. 修改配置文件指向您的数据文件
4. 启动程序开始自动更新
