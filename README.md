# TaoPower API 集群数据自动更新程序

## 📋 功能说明

这个程序可以从CSV文件中读取集群数据，每天自动更新到TaoPower API。

## 🚀 快速开始

### 1. 运行程序
```bash
双击运行 start.bat
```

### 2. 数据文件
- 程序使用 `clusters.csv` 作为数据源
- 包含20个必需字段（id, ping, 各种算力和资源数据）
- 程序会按行顺序每天更新一行数据

### 3. 配置修改
在 `config.py` 文件中可以修改：
- 更新时间（默认上午9:00）
- 数据文件路径
- API密钥等设置

## 📊 CSV文件格式

必需的列名：
- id, ping
- total_flops, used_flops, available_flops
- total_cpu_cores, used_cpu_cores, available_cpu_cores  
- total_mem_size, used_mem_size, available_mem_size
- total_gpu_cards, used_gpu_cards, available_gpu_cards
- total_gpu_mem_size, used_gpu_mem_size, available_gpu_mem_size
- total_storage_size, used_storage_size, available_storage_size

## 📝 日志监控

程序运行日志保存在 `cluster_updater.log` 文件中。

## 🔧 故障排除

1. **程序无法启动**：检查Python环境和依赖包
2. **CSV文件读取失败**：检查文件格式和列名
3. **API调用失败**：检查网络连接和API密钥

## 📁 文件说明

- `cluster_data_updater.py` - 主程序
- `config.py` - 配置文件  
- `clusters.csv` - 数据文件
- `start.bat` - 启动脚本
- `create_sample_csv.py` - 示例数据生成器
