# TaoPower API 集群数据自动更新程序

这个程序可以从 `clusters.xlsx` 文件中按时间顺序读取数据，每天自动更新一次数据到 TaoPower API。

## 功能特性

- 📊 从 Excel 文件读取集群数据
- 🔄 每天定时自动更新数据到 TaoPower API
- 🔐 支持 HMAC-SHA256 签名认证
- 📝 详细的日志记录
- ⚙️ 可配置的更新时间和参数
- 🔁 数据循环使用（到达末尾后重新开始）

## 安装依赖

```bash
pip install -r requirements.txt
```

## Excel 文件格式

`clusters.xlsx` 文件应包含以下列：

| 列名 | 描述 | 类型 | 必填 |
|------|------|------|------|
| id | 集群ID | 字符串 | 是 |
| ping | ping值 | 整数 | 是 |
| total_flops | 总算力(TFLOPS) | 整数 | 是 |
| used_flops | 已用算力(TFLOPS) | 整数 | 是 |
| available_flops | 可用算力(TFLOPS) | 整数 | 是 |
| total_cpu_cores | 总CPU核数 | 整数 | 是 |
| used_cpu_cores | 已用CPU核数 | 整数 | 是 |
| available_cpu_cores | 可用CPU核数 | 整数 | 是 |
| total_mem_size | 总内存(GB) | 整数 | 是 |
| used_mem_size | 已用内存(GB) | 整数 | 是 |
| available_mem_size | 可用内存(GB) | 整数 | 是 |
| total_gpu_cards | 总GPU卡数 | 整数 | 是 |
| used_gpu_cards | 已用GPU卡数 | 整数 | 是 |
| available_gpu_cards | 可用GPU卡数 | 整数 | 是 |
| total_gpu_mem_size | 总GPU内存(GB) | 整数 | 是 |
| used_gpu_mem_size | 已用GPU内存(GB) | 整数 | 是 |
| available_gpu_mem_size | 可用GPU内存(GB) | 整数 | 是 |
| total_storage_size | 总存储(GB) | 整数 | 是 |
| used_storage_size | 已用存储(GB) | 整数 | 是 |
| available_storage_size | 可用存储(GB) | 整数 | 是 |

## 配置

在 `config.py` 文件中可以修改以下配置：

- **API配置**: ACCESS_KEY, SECRET_KEY, BASE_URL
- **文件配置**: Excel文件路径，日志文件路径
- **调度配置**: 更新时间，检查间隔
- **列名映射**: Excel列名与API字段的映射关系

## 运行程序

```bash
python cluster_data_updater.py
```

程序启动后会：
1. 立即执行一次数据更新（用于测试）
2. 每天在指定时间（默认上午9:00）自动更新数据
3. 按顺序读取Excel中的每一行数据
4. 到达数据末尾后重新从第一行开始

## 日志

程序会在 `cluster_updater.log` 文件中记录详细的运行日志，包括：
- 程序启动信息
- 数据加载状态
- API调用结果
- 错误信息

## 注意事项

1. 确保 `clusters.xlsx` 文件存在且格式正确
2. 检查网络连接，确保可以访问 TaoPower API
3. 验证 API 密钥的有效性
4. 程序需要持续运行以执行定时任务

## 故障排除

- **Excel文件读取失败**: 检查文件是否存在，格式是否正确
- **API调用失败**: 检查网络连接和API密钥
- **签名验证失败**: 确认时间戳和签名算法正确
- **数据格式错误**: 检查Excel中的数据类型是否符合要求
