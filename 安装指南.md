# 安装指南

## 系统要求

- Windows 10/11
- Python 3.7 或更高版本

## 第一步：安装Python

### 方法一：从官网下载（推荐）

1. 访问 [Python官网](https://www.python.org/downloads/)
2. 下载最新版本的Python（建议3.9+）
3. 运行安装程序时，**务必勾选"Add Python to PATH"**
4. 选择"Install Now"进行安装

### 方法二：使用Microsoft Store

1. 打开Microsoft Store
2. 搜索"Python"
3. 安装Python 3.x版本

## 第二步：验证安装

打开命令提示符（cmd）或PowerShell，输入：

```bash
python --version
```

应该显示类似：`Python 3.9.x`

```bash
pip --version
```

应该显示pip版本信息

## 第三步：安装项目依赖

在项目目录中运行：

```bash
pip install pandas openpyxl requests schedule
```

或者使用requirements.txt：

```bash
pip install -r requirements.txt
```

## 第四步：创建示例Excel文件

```bash
python create_sample_excel.py
```

## 第五步：测试API连接

```bash
python test_api.py
```

## 第六步：运行主程序

```bash
python cluster_data_updater.py
```

## 常见安装问题

### 问题1：'python' 不是内部或外部命令

**解决方案：**
- 重新安装Python，确保勾选"Add Python to PATH"
- 或者手动添加Python到系统PATH环境变量

### 问题2：pip安装包失败

**解决方案：**
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple pandas openpyxl requests schedule
```

### 问题3：权限错误

**解决方案：**
- 以管理员身份运行命令提示符
- 或使用用户安装：`pip install --user package_name`

### 问题4：网络连接问题

**解决方案：**
```bash
# 使用代理
pip install --proxy http://proxy.server:port package_name

# 或使用离线安装包
pip install package_name.whl
```

## 手动安装依赖包

如果pip安装失败，可以手动下载whl文件：

1. 访问 [PyPI](https://pypi.org/)
2. 搜索并下载对应的.whl文件：
   - pandas
   - openpyxl  
   - requests
   - schedule
3. 使用pip安装本地文件：
   ```bash
   pip install path/to/package.whl
   ```

## 验证安装

运行以下Python代码验证所有包都已正确安装：

```python
try:
    import pandas as pd
    import requests
    import schedule
    import openpyxl
    print("✅ 所有依赖包安装成功！")
except ImportError as e:
    print(f"❌ 缺少依赖包: {e}")
```

## 下一步

安装完成后，请参考`项目说明.md`了解如何使用程序。
