@echo off
chcp 65001 >nul
title TaoPower集群数据更新程序管理
color 0A

:menu
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║        TaoPower集群数据更新程序        ║
echo ║              管理控制台              ║
echo ╚══════════════════════════════════════╝
echo.
echo 📋 当前状态:
for /f %%i in ('tasklist /fi "imagename eq python.exe" ^| find /c "python.exe"') do set count=%%i
if %count% gtr 0 (
    echo    🟢 程序运行中 ^(%count%个Python进程^)
) else (
    echo    🔴 程序未运行
)
echo.
echo 🔧 操作选项:
echo    [1] 启动程序 ^(后台运行^)
echo    [2] 停止程序
echo    [3] 重启程序
echo    [4] 查看日志 ^(最新10行^)
echo    [5] 查看完整日志
echo    [6] 查看程序状态
echo    [0] 退出
echo.
set /p choice=请选择操作 ^(0-6^): 

if "%choice%"=="1" goto start_program
if "%choice%"=="2" goto stop_program
if "%choice%"=="3" goto restart_program
if "%choice%"=="4" goto view_log
if "%choice%"=="5" goto view_full_log
if "%choice%"=="6" goto check_status
if "%choice%"=="0" goto exit
goto menu

:start_program
echo.
echo 🚀 启动程序...
start /b /min "" "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" "cluster_data_updater.py"
timeout /t 3 >nul
echo ✅ 程序已启动（后台运行）
pause
goto menu

:stop_program
echo.
echo 🛑 停止程序...
taskkill /f /im python.exe >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 程序已停止
) else (
    echo ℹ️  没有找到运行中的程序
)
pause
goto menu

:restart_program
echo.
echo 🔄 重启程序...
taskkill /f /im python.exe >nul 2>&1
timeout /t 2 >nul
start /b /min "" "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" "cluster_data_updater.py"
timeout /t 3 >nul
echo ✅ 程序已重启
pause
goto menu

:view_log
echo.
echo 📄 最新日志 ^(最后10行^):
echo ----------------------------------------
if exist cluster_updater.log (
    powershell "Get-Content cluster_updater.log -Tail 10"
) else (
    echo ❌ 日志文件不存在
)
echo ----------------------------------------
pause
goto menu

:view_full_log
echo.
echo 📄 打开完整日志文件...
if exist cluster_updater.log (
    notepad cluster_updater.log
) else (
    echo ❌ 日志文件不存在
    pause
)
goto menu

:check_status
echo.
echo 📊 程序详细状态:
echo ----------------------------------------
echo 🔍 Python进程:
tasklist /fi "imagename eq python.exe" 2>nul | findstr python.exe
if %errorlevel% neq 0 echo    没有运行中的Python进程

echo.
echo 📁 文件状态:
if exist cluster_data_updater.py (echo    ✅ 主程序文件存在) else (echo    ❌ 主程序文件缺失)
if exist config.py (echo    ✅ 配置文件存在) else (echo    ❌ 配置文件缺失)
if exist clusters.csv (echo    ✅ 数据文件存在) else (echo    ❌ 数据文件缺失)
if exist cluster_updater.log (echo    ✅ 日志文件存在) else (echo    ❌ 日志文件缺失)

echo.
echo 📈 最后更新时间:
if exist cluster_updater.log (
    echo    日志文件最后修改: 
    forfiles /m cluster_updater.log /c "cmd /c echo @fdate @ftime"
)
echo ----------------------------------------
pause
goto menu

:exit
echo.
echo 👋 再见！
timeout /t 2 >nul
exit
