#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查CSV文件格式和内容
"""

import pandas as pd
from datetime import datetime
import os
from config import CSV_COLUMNS

def check_csv_file(filename="clusters.csv"):
    """检查CSV文件"""
    print(f"=== 检查CSV文件: {filename} ===")
    
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        return False
    
    try:
        # 读取CSV文件
        df = pd.read_csv(filename)
        print(f"✅ 成功读取CSV文件")
        print(f"📊 数据行数: {len(df)}")
        print(f"📋 列数: {len(df.columns)}")
        
        # 显示列名
        print(f"\n📝 列名列表:")
        for i, col in enumerate(df.columns, 1):
            print(f"{i:2d}. {col}")
        
        # 检查必需的列
        required_columns = [
            'id', 'ping', 'total_flops', 'used_flops', 'available_flops',
            'total_cpu_cores', 'used_cpu_cores', 'available_cpu_cores',
            'total_mem_size', 'used_mem_size', 'available_mem_size',
            'total_gpu_cards', 'used_gpu_cards', 'available_gpu_cards',
            'total_gpu_mem_size', 'used_gpu_mem_size', 'available_gpu_mem_size',
            'total_storage_size', 'used_storage_size', 'available_storage_size'
        ]
        
        print(f"\n🔍 检查必需列:")
        missing_columns = []
        for col in required_columns:
            if col in df.columns:
                print(f"✅ {col}")
            else:
                print(f"❌ {col} - 缺失")
                missing_columns.append(col)
        
        # 检查日期列
        print(f"\n📅 检查日期列:")
        date_columns = ['date', 'Date', 'DATE', '日期', 'update_date', 'time']
        found_date_column = None
        for col in date_columns:
            if col in df.columns:
                found_date_column = col
                print(f"✅ 找到日期列: {col}")
                
                # 尝试解析日期
                try:
                    df[col] = pd.to_datetime(df[col], errors='coerce')
                    valid_dates = df[col].dropna()
                    print(f"📊 有效日期数: {len(valid_dates)}")
                    
                    if len(valid_dates) > 0:
                        print(f"📅 日期范围: {valid_dates.min().date()} 到 {valid_dates.max().date()}")
                        
                        # 检查今天的数据
                        today = datetime.now().date()
                        today_data = df[df[col].dt.date == today]
                        print(f"🎯 今天({today})的数据: {len(today_data)}行")
                        
                        if len(today_data) > 0:
                            print("✅ 找到今天的数据")
                        else:
                            # 找最近的日期
                            date_diffs = abs(valid_dates.dt.date - today)
                            nearest_date = valid_dates.iloc[date_diffs.argmin()].date()
                            print(f"⚠️  今天无数据，最近日期: {nearest_date}")
                    
                except Exception as e:
                    print(f"❌ 日期解析失败: {e}")
                break
        
        if not found_date_column:
            print("⚠️  未找到日期列，将使用顺序模式")
        
        # 显示前几行数据
        print(f"\n📋 前3行数据预览:")
        print(df.head(3).to_string(index=False))
        
        # 检查数据类型
        print(f"\n🔢 数据类型检查:")
        numeric_columns = [col for col in required_columns if col not in ['id']]
        for col in numeric_columns:
            if col in df.columns:
                try:
                    pd.to_numeric(df[col], errors='raise')
                    print(f"✅ {col}: 数值类型正确")
                except:
                    print(f"⚠️  {col}: 包含非数值数据")
        
        if missing_columns:
            print(f"\n❌ 缺失列: {missing_columns}")
            return False
        else:
            print(f"\n🎉 CSV文件格式检查通过!")
            return True
            
    except Exception as e:
        print(f"❌ 读取CSV文件失败: {e}")
        return False

def main():
    """主函数"""
    print("CSV文件格式检查工具")
    print("=" * 50)
    
    # 检查当前目录的clusters.csv
    success = check_csv_file("clusters.csv")
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 文件检查通过，可以正常使用!")
    else:
        print("❌ 文件检查失败，请修正后再试!")

if __name__ == "__main__":
    main()
