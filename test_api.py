#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TaoPower API 测试脚本
用于测试API连接和签名算法
"""

import json
import hmac
import hashlib
import time
import requests
from config import API_CONFIG

def test_signature():
    """测试签名算法"""
    print("=== 测试签名算法 ===")
    
    # 测试数据
    access_key = API_CONFIG['ACCESS_KEY']
    secret_key = API_CONFIG['SECRET_KEY']
    ts = 1723448959001
    query_string = "a3b2c4d1"
    body = '{"dummy": "val"}'
    
    # 构建待签名字符串
    to_sign = f"{ts}{access_key}{query_string}{body}"
    print(f"待签名字符串: {to_sign}")
    
    # 生成签名
    signature = hmac.new(
        secret_key.encode('utf-8'),
        to_sign.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    print(f"生成的签名: {signature}")
    return signature

def test_api_connection():
    """测试API连接"""
    print("\n=== 测试API连接 ===")
    
    access_key = API_CONFIG['ACCESS_KEY']
    secret_key = API_CONFIG['SECRET_KEY']
    base_url = API_CONFIG['BASE_URL']
    
    # 构建测试数据
    test_data = {
        "clusters": [
            {
                "id": "test_cluster_001",
                "ping": 10,
                "total_flops": 1000,
                "used_flops": 100,
                "available_flops": 900,
                "total_cpu_cores": 64,
                "used_cpu_cores": 8,
                "available_cpu_cores": 56,
                "total_mem_size": 512,
                "used_mem_size": 64,
                "available_mem_size": 448,
                "total_gpu_cards": 8,
                "used_gpu_cards": 1,
                "available_gpu_cards": 7,
                "total_gpu_mem_size": 640,
                "used_gpu_mem_size": 80,
                "available_gpu_mem_size": 560,
                "total_storage_size": 10000,
                "used_storage_size": 1000,
                "available_storage_size": 9000
            }
        ]
    }
    
    # 生成签名
    body = json.dumps(test_data, ensure_ascii=False)
    ts = int(time.time() * 1000)
    to_sign = f"{ts}{access_key}{body}"
    
    signature = hmac.new(
        secret_key.encode('utf-8'),
        to_sign.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    # 构建请求头
    headers = {
        "Content-Type": "application/json",
        "ts": str(ts),
        "accessKey": access_key,
        "sign": signature
    }
    
    print(f"请求URL: {base_url}/powerapi/v1/clusters")
    print(f"请求头: {json.dumps(headers, indent=2, ensure_ascii=False)}")
    print(f"请求体: {body}")
    
    try:
        # 发送请求
        url = f"{base_url}/powerapi/v1/clusters"
        response = requests.post(url, headers=headers, data=body, timeout=30)
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ API测试成功!")
                return True
            else:
                print(f"❌ API返回错误: {result}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        return False

def main():
    """主函数"""
    print("TaoPower API 测试工具")
    print("=" * 50)
    
    # 测试签名算法
    test_signature()
    
    # 测试API连接
    success = test_api_connection()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！程序可以正常运行。")
    else:
        print("⚠️  测试失败，请检查配置和网络连接。")

if __name__ == "__main__":
    main()
