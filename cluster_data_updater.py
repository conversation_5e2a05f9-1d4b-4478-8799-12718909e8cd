#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TaoPower API 集群数据自动更新程序
根据clusters.xlsx文件中的时间顺序，每天更新一次数据到TaoPower API
"""

import pandas as pd
import requests
import json
import hmac
import hashlib
import time
import schedule
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
import os
from config import API_CONFIG, FILE_CONFIG, SCHEDULE_CONFIG, EXCEL_COLUMNS
import urllib3

# 禁用SSL警告（仅用于测试）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(FILE_CONFIG['LOG_FILE'], encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class TaoPowerAPIClient:
    """TaoPower API客户端"""
    
    def __init__(self, access_key: str, secret_key: str):
        self.access_key = access_key
        self.secret_key = secret_key
        self.base_url = API_CONFIG['BASE_URL']
        
    def generate_signature(self, ts: int, query_string: str = "", body: str = "") -> str:
        """生成API签名"""
        # 构建待签名字符串: ts + accessKey + queryString + body
        to_sign = f"{ts}{self.access_key}{query_string}{body}"
        
        # 使用HMAC-SHA256生成签名
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            to_sign.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    def get_headers(self, body: str = "") -> Dict[str, str]:
        """获取请求头"""
        ts = int(time.time() * 1000)  # 毫秒级时间戳
        sign = self.generate_signature(ts, "", body)
        
        return {
            "Content-Type": "application/json",
            "ts": str(ts),
            "accessKey": self.access_key,
            "sign": sign
        }
    
    def update_clusters(self, clusters_data: List[Dict[str, Any]]) -> bool:
        """更新集群数据"""
        url = f"{self.base_url}/powerapi/v1/clusters"
        payload = {"clusters": clusters_data}
        body = json.dumps(payload, ensure_ascii=False)
        
        headers = self.get_headers(body)
        
        try:
            # 跳过SSL验证（仅用于测试）
            response = requests.post(url, headers=headers, data=body, timeout=30, verify=False)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 200:
                logging.info(f"集群数据更新成功: {result.get('msg')}")
                return True
            else:
                logging.error(f"集群数据更新失败: {result}")
                return False
                
        except requests.exceptions.RequestException as e:
            logging.error(f"API请求失败: {e}")
            return False
        except json.JSONDecodeError as e:
            logging.error(f"响应JSON解析失败: {e}")
            return False

class ClusterDataManager:
    """集群数据管理器"""
    
    def __init__(self, excel_file: str, api_client: TaoPowerAPIClient):
        self.excel_file = excel_file
        self.api_client = api_client
        self.current_row_index = 0
        self.data_df = None
        
    def load_excel_data(self) -> bool:
        """加载Excel数据"""
        try:
            if not os.path.exists(self.excel_file):
                logging.error(f"Excel文件不存在: {self.excel_file}")
                return False
                
            self.data_df = pd.read_excel(self.excel_file)
            logging.info(f"成功加载Excel文件，共{len(self.data_df)}行数据")
            return True
            
        except Exception as e:
            logging.error(f"加载Excel文件失败: {e}")
            return False
    
    def get_today_data(self) -> List[Dict[str, Any]]:
        """获取今天要更新的数据"""
        if self.data_df is None or self.data_df.empty:
            logging.warning("没有可用的数据")
            return []
        
        # 如果已经到达数据末尾，重新开始
        if self.current_row_index >= len(self.data_df):
            self.current_row_index = 0
            logging.info("数据已循环完毕，重新开始")
        
        # 获取当前行数据
        row = self.data_df.iloc[self.current_row_index]
        
        # 构建集群数据（根据Excel列名调整）
        cluster_data = {}
        for api_field, excel_column in EXCEL_COLUMNS.items():
            if api_field == "id":
                cluster_data[api_field] = str(row.get(excel_column, f"cluster_{self.current_row_index}"))
            elif api_field == "ping":
                cluster_data[api_field] = int(row.get(excel_column, 10))
            else:
                cluster_data[api_field] = int(row.get(excel_column, 0))
        
        # 移动到下一行
        self.current_row_index += 1
        
        logging.info(f"获取第{self.current_row_index}行数据，集群ID: {cluster_data['id']}")
        return [cluster_data]
    
    def update_daily_data(self):
        """执行每日数据更新"""
        logging.info("开始执行每日数据更新...")
        
        # 重新加载Excel数据（以防文件被更新）
        if not self.load_excel_data():
            return
        
        # 获取今天的数据
        today_data = self.get_today_data()
        if not today_data:
            logging.warning("没有获取到今天的数据")
            return
        
        # 更新到API
        success = self.api_client.update_clusters(today_data)
        if success:
            logging.info("每日数据更新完成")
        else:
            logging.error("每日数据更新失败")

def main():
    """主函数"""
    # 从配置文件获取设置
    ACCESS_KEY = API_CONFIG['ACCESS_KEY']
    SECRET_KEY = API_CONFIG['SECRET_KEY']
    EXCEL_FILE = FILE_CONFIG['EXCEL_FILE']

    # 初始化API客户端和数据管理器
    api_client = TaoPowerAPIClient(ACCESS_KEY, SECRET_KEY)
    data_manager = ClusterDataManager(EXCEL_FILE, api_client)
    
    # 加载初始数据
    if not data_manager.load_excel_data():
        logging.error("程序启动失败：无法加载Excel数据")
        return
    
    # 设置定时任务
    update_time = SCHEDULE_CONFIG['UPDATE_TIME']
    schedule.every().day.at(update_time).do(data_manager.update_daily_data)

    logging.info("集群数据更新程序已启动")
    logging.info(f"每日更新时间：{update_time}")
    
    # 立即执行一次更新（用于测试）
    logging.info("执行初始数据更新...")
    data_manager.update_daily_data()
    
    # 主循环
    check_interval = SCHEDULE_CONFIG['CHECK_INTERVAL']
    while True:
        schedule.run_pending()
        time.sleep(check_interval)

if __name__ == "__main__":
    main()
