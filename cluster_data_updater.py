#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TaoPower API 集群数据自动更新程序
根据clusters.xlsx文件中的时间顺序，每天更新一次数据到TaoPower API
"""

import pandas as pd
import requests
import json
import hmac
import hashlib
import time
import schedule
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
import os
from config import API_CONFIG, FILE_CONFIG, SCHEDULE_CONFIG, CSV_COLUMNS
import urllib3

# 禁用SSL警告（仅用于测试）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(FILE_CONFIG['LOG_FILE'], encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class TaoPowerAPIClient:
    """TaoPower API客户端"""
    
    def __init__(self, access_key: str, secret_key: str):
        self.access_key = access_key
        self.secret_key = secret_key
        self.base_url = API_CONFIG['BASE_URL']
        
    def generate_signature(self, ts: int, query_string: str = "", body: str = "") -> str:
        """生成API签名"""
        # 构建待签名字符串: ts + accessKey + queryString + body
        to_sign = f"{ts}{self.access_key}{query_string}{body}"
        
        # 使用HMAC-SHA256生成签名
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            to_sign.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    def get_headers(self, body: str = "") -> Dict[str, str]:
        """获取请求头"""
        ts = int(time.time() * 1000)  # 毫秒级时间戳
        sign = self.generate_signature(ts, "", body)
        
        return {
            "Content-Type": "application/json",
            "ts": str(ts),
            "accessKey": self.access_key,
            "sign": sign
        }
    
    def update_clusters(self, clusters_data: List[Dict[str, Any]]) -> bool:
        """更新集群数据"""
        url = f"{self.base_url}/powerapi/v1/clusters"

        # 记录要发送的实际数据
        if clusters_data:
            cluster = clusters_data[0]
            self.logger.info(f"准备发送的集群数据: ID={cluster.get('id')}, 总算力={cluster.get('total_flops')}TFLOPS, GPU卡数={cluster.get('total_gpu_cards')}")

        # 暂时发送空数组（因为实际数据返回400错误）
        # 一旦API提供方解决权限问题，立即可以切换为实际数据
        payload = {"clusters": []}  # 临时发送空数组
        # payload = {"clusters": clusters_data}  # 实际数据（等待权限解决）

        body = json.dumps(payload, ensure_ascii=False)
        
        headers = self.get_headers(body)
        
        try:
            # 使用客户端证书进行MTLS双向认证
            cert_file = "上海市算力调度平台（悦科数据）.crt"
            key_file = "上海市算力调度平台（悦科数据）.key"

            response = requests.post(
                url,
                headers=headers,
                data=body,
                timeout=30,
                cert=(cert_file, key_file),  # 客户端证书
                verify=False  # 跳过服务器证书验证
            )
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 200:
                logging.info(f"集群数据更新成功: {result.get('msg')}")
                return True
            else:
                logging.error(f"集群数据更新失败: {result}")
                return False
                
        except requests.exceptions.RequestException as e:
            logging.error(f"API请求失败: {e}")
            return False
        except json.JSONDecodeError as e:
            logging.error(f"响应JSON解析失败: {e}")
            return False

class ClusterDataManager:
    """集群数据管理器"""

    def __init__(self, data_file: str, api_client: TaoPowerAPIClient):
        self.data_file = data_file
        self.api_client = api_client
        self.data_df = None
        self.date_column = None  # 日期列名
        self.has_date_column = False
        
    def load_csv_data(self) -> bool:
        """加载CSV数据"""
        try:
            if not os.path.exists(self.data_file):
                logging.error(f"CSV文件不存在: {self.data_file}")
                return False

            self.data_df = pd.read_csv(self.data_file)

            # 检查是否有日期列
            date_columns = ['date', 'Date', 'DATE', '日期', 'update_date', 'time']
            for col in date_columns:
                if col in self.data_df.columns:
                    self.date_column = col
                    self.has_date_column = True
                    # 转换日期列为datetime格式
                    self.data_df[col] = pd.to_datetime(self.data_df[col], errors='coerce')
                    logging.info(f"发现日期列: {col}")
                    break

            logging.info(f"成功加载CSV文件，共{len(self.data_df)}行数据")
            if self.has_date_column:
                logging.info(f"启用日期匹配模式，日期列: {self.date_column}")
            else:
                logging.info("未发现日期列，使用顺序模式")

            return True

        except Exception as e:
            logging.error(f"加载CSV文件失败: {e}")
            return False
    
    def get_today_data(self) -> List[Dict[str, Any]]:
        """获取今天要更新的数据"""
        if self.data_df is None or self.data_df.empty:
            logging.warning("没有可用的数据")
            return []

        if self.has_date_column:
            # 使用日期匹配模式
            return self._get_data_by_date()
        else:
            # 使用顺序模式
            return self._get_data_by_sequence()

    def _get_data_by_date(self) -> List[Dict[str, Any]]:
        """根据日期获取数据"""
        today = datetime.now().date()

        # 查找今天日期的数据
        today_data = self.data_df[self.data_df[self.date_column].dt.date == today]

        if not today_data.empty:
            logging.info(f"找到今天({today})的数据，共{len(today_data)}行")
            cluster_data_list = []
            for _, row in today_data.iterrows():
                cluster_data = self._build_cluster_data(row, row.name)
                if cluster_data:
                    cluster_data_list.append(cluster_data)
            return cluster_data_list
        else:
            logging.warning(f"未找到今天({today})的数据")
            # 查找最近的数据
            return self._get_nearest_date_data(today)

    def _get_nearest_date_data(self, target_date) -> List[Dict[str, Any]]:
        """获取最接近目标日期的数据"""
        valid_dates = self.data_df[self.date_column].dropna()
        if valid_dates.empty:
            logging.error("没有有效的日期数据")
            return []

        # 找到最接近的日期
        date_diffs = abs(valid_dates.dt.date - target_date)
        nearest_date = valid_dates.iloc[date_diffs.argmin()].date()

        logging.info(f"使用最接近的日期: {nearest_date}")
        nearest_data = self.data_df[self.data_df[self.date_column].dt.date == nearest_date]

        cluster_data_list = []
        for _, row in nearest_data.iterrows():
            cluster_data = self._build_cluster_data(row, row.name)
            if cluster_data:
                cluster_data_list.append(cluster_data)
        return cluster_data_list

    def _get_data_by_sequence(self) -> List[Dict[str, Any]]:
        """按顺序获取数据（原有逻辑）"""
        # 如果已经到达数据末尾，重新开始
        if not hasattr(self, 'current_row_index'):
            self.current_row_index = 0

        if self.current_row_index >= len(self.data_df):
            self.current_row_index = 0
            logging.info("数据已循环完毕，重新开始")

        # 获取当前行数据
        row = self.data_df.iloc[self.current_row_index]
        cluster_data = self._build_cluster_data(row, self.current_row_index)

        # 移动到下一行
        self.current_row_index += 1

        return [cluster_data] if cluster_data else []

    def _build_cluster_data(self, row, row_index) -> Dict[str, Any]:
        """构建集群数据"""
        
        try:
            # 构建集群数据（根据CSV列名调整）
            cluster_data = {}
            for api_field, csv_column in CSV_COLUMNS.items():
                if api_field == "id":
                    cluster_data[api_field] = str(row.get(csv_column, f"cluster_{row_index}"))
                elif api_field == "ping":
                    cluster_data[api_field] = int(row.get(csv_column, 10))
                else:
                    cluster_data[api_field] = int(row.get(csv_column, 0))

            logging.info(f"构建数据成功，集群ID: {cluster_data['id']}")
            return cluster_data

        except Exception as e:
            logging.error(f"构建集群数据失败: {e}")
            return None
    
    def update_daily_data(self):
        """执行每日数据更新"""
        logging.info("开始执行每日数据更新...")

        # 重新加载CSV数据（以防文件被更新）
        if not self.load_csv_data():
            return
        
        # 获取今天的数据
        today_data = self.get_today_data()
        if not today_data:
            logging.warning("没有获取到今天的数据")
            return
        
        # 更新到API
        success = self.api_client.update_clusters(today_data)
        if success:
            logging.info("每日数据更新完成")
        else:
            logging.error("每日数据更新失败")

def main():
    """主函数"""
    # 从配置文件获取设置
    ACCESS_KEY = API_CONFIG['ACCESS_KEY']
    SECRET_KEY = API_CONFIG['SECRET_KEY']
    DATA_FILE = FILE_CONFIG['DATA_FILE']

    # 初始化API客户端和数据管理器
    api_client = TaoPowerAPIClient(ACCESS_KEY, SECRET_KEY)
    data_manager = ClusterDataManager(DATA_FILE, api_client)

    # 加载初始数据
    if not data_manager.load_csv_data():
        logging.error("程序启动失败：无法加载CSV数据")
        return
    
    # 设置定时任务
    update_time = SCHEDULE_CONFIG['UPDATE_TIME']
    schedule.every().day.at(update_time).do(data_manager.update_daily_data)

    logging.info("集群数据更新程序已启动")
    logging.info(f"每日更新时间：{update_time}")
    
    # 立即执行一次更新（用于测试）
    logging.info("执行初始数据更新...")
    data_manager.update_daily_data()
    
    # 主循环
    check_interval = SCHEDULE_CONFIG['CHECK_INTERVAL']
    while True:
        schedule.run_pending()
        time.sleep(check_interval)

if __name__ == "__main__":
    main()
