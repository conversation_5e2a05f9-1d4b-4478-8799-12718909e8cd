#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日期匹配功能
模拟不同日期，验证程序能否正确获取对应日期的数据
"""

import pandas as pd
from datetime import datetime, timedelta
import logging
from config import CSV_COLUMNS, FILE_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def load_csv_data(file_path):
    """加载CSV数据"""
    try:
        df = pd.read_csv(file_path)
        logging.info(f"成功加载CSV文件，共{len(df)}行数据")
        
        # 检查是否有日期列
        date_columns = ['日期', 'date', 'Date', 'DATE', 'update_date', 'time']
        date_column = None
        for col in date_columns:
            if col in df.columns:
                date_column = col
                logging.info(f"发现日期列: {col}")
                # 转换日期列为datetime格式
                df[col] = pd.to_datetime(df[col], errors='coerce')
                break
        
        if date_column:
            logging.info(f"启用日期匹配模式，日期列: {date_column}")
            return df, date_column
        else:
            logging.warning("未发现日期列")
            return df, None
            
    except Exception as e:
        logging.error(f"加载CSV文件失败: {e}")
        return None, None

def get_data_by_date(df, date_column, target_date):
    """根据日期获取数据"""
    if df is None or date_column is None:
        return None
    
    # 将目标日期转换为datetime
    if isinstance(target_date, str):
        target_date = datetime.strptime(target_date, "%Y-%m-%d").date()
    elif hasattr(target_date, 'date'):
        target_date = target_date.date()
    
    # 查找目标日期的数据
    target_data = df[df[date_column].dt.date == target_date]
    
    if not target_data.empty:
        logging.info(f"找到目标日期({target_date})的数据，共{len(target_data)}行")
        return target_data
    else:
        logging.warning(f"未找到目标日期({target_date})的数据")
        
        # 查找最近的日期
        valid_dates = df[date_column].dropna()
        if valid_dates.empty:
            logging.error("没有有效的日期数据")
            return None
        
        # 找到最接近的日期
        date_diffs = abs(valid_dates.dt.date - target_date)
        nearest_date = valid_dates.iloc[date_diffs.argmin()].date()
        
        logging.info(f"使用最接近的日期: {nearest_date}")
        nearest_data = df[df[date_column].dt.date == nearest_date]
        return nearest_data

def test_date_matching():
    """测试日期匹配功能"""
    # 加载CSV数据
    df, date_column = load_csv_data(FILE_CONFIG['DATA_FILE'])
    if df is None or date_column is None:
        return
    
    # 获取CSV文件中的日期范围
    min_date = df[date_column].min().date()
    max_date = df[date_column].max().date()
    logging.info(f"CSV文件日期范围: {min_date} 到 {max_date}")
    
    # 测试用例1: 今天的日期
    today = datetime.now().date()
    logging.info(f"\n=== 测试用例1: 今天({today}) ===")
    today_data = get_data_by_date(df, date_column, today)
    if today_data is not None and not today_data.empty:
        row = today_data.iloc[0]
        print(f"今天的数据: 集群ID={row['id']}, 总算力={row['total_flops']}, GPU卡数={row['total_gpu_cards']}")
    
    # 测试用例2: 明天的日期
    tomorrow = today + timedelta(days=1)
    logging.info(f"\n=== 测试用例2: 明天({tomorrow}) ===")
    tomorrow_data = get_data_by_date(df, date_column, tomorrow)
    if tomorrow_data is not None and not tomorrow_data.empty:
        row = tomorrow_data.iloc[0]
        print(f"明天的数据: 集群ID={row['id']}, 总算力={row['total_flops']}, GPU卡数={row['total_gpu_cards']}")
    
    # 测试用例3: 一周后的日期
    next_week = today + timedelta(days=7)
    logging.info(f"\n=== 测试用例3: 一周后({next_week}) ===")
    next_week_data = get_data_by_date(df, date_column, next_week)
    if next_week_data is not None and not next_week_data.empty:
        row = next_week_data.iloc[0]
        print(f"一周后的数据: 集群ID={row['id']}, 总算力={row['total_flops']}, GPU卡数={row['total_gpu_cards']}")
    
    # 测试用例4: 超出范围的日期
    future_date = max_date + timedelta(days=30)
    logging.info(f"\n=== 测试用例4: 超出范围({future_date}) ===")
    future_data = get_data_by_date(df, date_column, future_date)
    if future_data is not None and not future_data.empty:
        row = future_data.iloc[0]
        print(f"超出范围的数据: 集群ID={row['id']}, 总算力={row['total_flops']}, GPU卡数={row['total_gpu_cards']}")
    
    # 测试用例5: 过去的日期
    past_date = min_date - timedelta(days=30)
    logging.info(f"\n=== 测试用例5: 过去日期({past_date}) ===")
    past_data = get_data_by_date(df, date_column, past_date)
    if past_data is not None and not past_data.empty:
        row = past_data.iloc[0]
        print(f"过去日期的数据: 集群ID={row['id']}, 总算力={row['total_flops']}, GPU卡数={row['total_gpu_cards']}")

def main():
    """主函数"""
    print("=== 日期匹配功能测试 ===")
    test_date_matching()
    print("=== 测试完成 ===")

if __name__ == "__main__":
    main()
