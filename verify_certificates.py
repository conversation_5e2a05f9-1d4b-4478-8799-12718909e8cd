#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证证书文件并测试API上传
"""

import requests
import json
import hmac
import hashlib
import time
import os
import urllib3
from config import API_CONFIG

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def check_certificate_files():
    """检查证书文件"""
    print("=== 检查证书文件 ===")
    
    cert_files = {
        "客户端证书": "上海市算力调度平台（悦科数据）.crt",
        "客户端私钥": "上海市算力调度平台（悦科数据）.key", 
        "PKCS#12证书包": "上海市算力调度平台（悦科数据）.p12"
    }
    
    for desc, filename in cert_files.items():
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"✅ {desc}: {filename} ({size} 字节)")
            
            # 检查文件内容
            try:
                with open(filename, 'rb') as f:
                    content = f.read(100)  # 读取前100字节
                    if filename.endswith('.crt') or filename.endswith('.key'):
                        if b'-----BEGIN' in content:
                            print(f"   格式: PEM")
                        else:
                            print(f"   格式: 二进制/DER")
                    elif filename.endswith('.p12'):
                        print(f"   格式: PKCS#12")
            except Exception as e:
                print(f"   ⚠️  读取失败: {e}")
        else:
            print(f"❌ {desc}: {filename} - 文件不存在")
    
    return all(os.path.exists(f) for f in cert_files.values())

def test_api_with_certificates():
    """使用证书测试API"""
    print("\n=== 使用证书测试API ===")
    
    # 准备测试数据
    try:
        import pandas as pd
        df = pd.read_csv('clusters.csv')
        
        if not df.empty:
            # 获取今天的数据
            date_column = '日期'
            if date_column in df.columns:
                df[date_column] = pd.to_datetime(df[date_column], errors='coerce')
                today = pd.Timestamp.now().date()
                today_data = df[df[date_column].dt.date == today]
                
                if not today_data.empty:
                    row = today_data.iloc[0]
                    print(f"✅ 使用今天的数据: {today}")
                else:
                    row = df.iloc[0]
                    print(f"✅ 使用第一行数据")
            else:
                row = df.iloc[0]
                print(f"✅ 使用第一行数据")
            
            # 构建集群数据
            from config import CSV_COLUMNS
            cluster_data = {}
            for api_field, csv_column in CSV_COLUMNS.items():
                if api_field == "id":
                    cluster_data[api_field] = str(row.get(csv_column, "test_cluster"))
                elif api_field == "ping":
                    cluster_data[api_field] = int(row.get(csv_column, 10))
                else:
                    cluster_data[api_field] = int(row.get(csv_column, 0))
            
            print(f"集群ID: {cluster_data['id']}")
            print(f"总算力: {cluster_data['total_flops']} TFLOPS")
            print(f"总GPU卡数: {cluster_data['total_gpu_cards']}")
            
        else:
            print("⚠️  CSV文件为空，使用默认数据")
            cluster_data = {
                "id": "test_cluster_001",
                "ping": 10,
                "total_flops": 1000,
                "used_flops": 100,
                "available_flops": 900,
                "total_cpu_cores": 64,
                "used_cpu_cores": 8,
                "available_cpu_cores": 56,
                "total_mem_size": 512,
                "used_mem_size": 64,
                "available_mem_size": 448,
                "total_gpu_cards": 8,
                "used_gpu_cards": 1,
                "available_gpu_cards": 7,
                "total_gpu_mem_size": 640,
                "used_gpu_mem_size": 80,
                "available_gpu_mem_size": 560,
                "total_storage_size": 10000,
                "used_storage_size": 1000,
                "available_storage_size": 9000
            }
    
    except Exception as e:
        print(f"⚠️  数据准备失败: {e}")
        cluster_data = {
            "id": "test_cluster_001",
            "ping": 10,
            "total_flops": 1000,
            "used_flops": 100,
            "available_flops": 900,
            "total_cpu_cores": 64,
            "used_cpu_cores": 8,
            "available_cpu_cores": 56,
            "total_mem_size": 512,
            "used_mem_size": 64,
            "available_mem_size": 448,
            "total_gpu_cards": 8,
            "used_gpu_cards": 1,
            "available_gpu_cards": 7,
            "total_gpu_mem_size": 640,
            "used_gpu_mem_size": 80,
            "available_gpu_mem_size": 560,
            "total_storage_size": 10000,
            "used_storage_size": 1000,
            "available_storage_size": 9000
        }
    
    test_data = {"clusters": [cluster_data]}
    body = json.dumps(test_data, separators=(',', ':'))
    
    # 生成签名
    ts = int(time.time() * 1000)
    access_key = API_CONFIG['ACCESS_KEY']
    secret_key = API_CONFIG['SECRET_KEY']
    
    to_sign = f"{ts}{access_key}{body}"
    signature = hmac.new(
        secret_key.encode('utf-8'),
        to_sign.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    headers = {
        "Content-Type": "application/json",
        "ts": str(ts),
        "accessKey": access_key,
        "sign": signature
    }
    
    url = f"{API_CONFIG['BASE_URL']}/powerapi/v1/clusters"
    
    print(f"\n请求详情:")
    print(f"URL: {url}")
    print(f"时间戳: {ts}")
    print(f"签名: {signature[:20]}...")
    print(f"数据大小: {len(body)} 字节")
    
    # 测试不同的证书配置
    cert_configs = [
        {
            "name": "使用.crt和.key文件",
            "cert": ("上海市算力调度平台（悦科数据）.crt", "上海市算力调度平台（悦科数据）.key"),
            "verify": False
        },
        {
            "name": "跳过SSL验证",
            "cert": None,
            "verify": False
        },
        {
            "name": "默认SSL设置",
            "cert": None,
            "verify": True
        }
    ]
    
    for config in cert_configs:
        print(f"\n--- {config['name']} ---")
        try:
            kwargs = {
                "headers": headers,
                "data": body,
                "timeout": 30,
                "verify": config["verify"]
            }
            
            if config["cert"]:
                kwargs["cert"] = config["cert"]
                print(f"使用证书: {config['cert'][0]} + {config['cert'][1]}")
            
            response = requests.post(url, **kwargs)
            
            print(f"✅ 请求发送成功")
            print(f"状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get("code") == 200:
                        print("🎉 API调用完全成功!")
                        print("✅ 证书认证成功，数据上传成功!")
                        return True
                    else:
                        print(f"API返回错误: {result}")
                        if result.get("code") == 401:
                            print("可能是认证失败")
                        elif result.get("code") == 403:
                            print("可能是权限不足或IP未授权")
                except json.JSONDecodeError:
                    print("响应不是有效的JSON")
            elif response.status_code == 401:
                print("❌ 认证失败 - 检查API密钥或证书")
            elif response.status_code == 403:
                print("❌ 权限不足 - 检查IP白名单")
            elif response.status_code == 404:
                print("❌ 接口不存在 - 检查URL路径")
            elif response.status_code == 500:
                print("❌ 服务器内部错误")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except requests.exceptions.SSLError as e:
            print(f"❌ SSL错误: {e}")
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 连接错误: {e}")
        except requests.exceptions.Timeout as e:
            print(f"❌ 超时错误: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")
    
    return False

def main():
    """主函数"""
    print("🔐 证书验证和API上传测试")
    print("=" * 60)
    
    # 检查证书文件
    cert_ok = check_certificate_files()
    
    if not cert_ok:
        print("\n❌ 证书文件检查失败，无法继续测试")
        return
    
    # 测试API上传
    success = test_api_with_certificates()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 证书验证成功！API可以正常调用。")
        print("✅ 程序已准备就绪，可以开始自动更新。")
        print("\n下一步:")
        print("1. 运行主程序: python cluster_data_updater.py")
        print("2. 或使用启动脚本: start.bat")
    else:
        print("❌ 证书验证失败或API调用失败")
        print("\n可能的原因:")
        print("1. 证书文件格式不正确")
        print("2. 证书与私钥不匹配")
        print("3. IP地址未在白名单中")
        print("4. API密钥无效")
        print("5. 服务器维护中")
        print("\n建议:")
        print("1. 联系API提供方确认证书配置")
        print("2. 确认IP白名单设置")
        print("3. 验证API密钥有效性")

if __name__ == "__main__":
    main()
