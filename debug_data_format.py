#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据格式问题
"""

import requests
import json
import hmac
import hashlib
import time
import urllib3
from config import API_CONFIG

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_api_call(data, description):
    """测试API调用"""
    cert_file = "上海市算力调度平台（悦科数据）.crt"
    key_file = "上海市算力调度平台（悦科数据）.key"
    url = f"{API_CONFIG['BASE_URL']}/powerapi/v1/clusters"
    
    try:
        body = json.dumps(data, separators=(',', ':'))
        
        ts = int(time.time() * 1000)
        access_key = API_CONFIG['ACCESS_KEY']
        secret_key = API_CONFIG['SECRET_KEY']
        
        to_sign = f"{ts}{access_key}{body}"
        signature = hmac.new(
            secret_key.encode('utf-8'),
            to_sign.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        headers = {
            "Content-Type": "application/json",
            "ts": str(ts),
            "accessKey": access_key,
            "sign": signature
        }
        
        response = requests.post(
            url,
            headers=headers,
            data=body,
            timeout=30,
            cert=(cert_file, key_file),
            verify=False
        )
        
        print(f"--- {description} ---")
        print(f"数据: {body}")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✅ 成功!")
                return True
            else:
                print(f"❌ 错误码: {result.get('code')}")
        
        return False
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_minimal_required_fields():
    """测试最小必需字段组合"""
    print("🔍 测试最小必需字段组合")
    
    # 根据API文档，尝试不同的必需字段组合
    test_cases = [
        {
            "desc": "只有id",
            "data": {"clusters": [{"id": "cluster_001"}]}
        },
        {
            "desc": "id + ping",
            "data": {"clusters": [{"id": "cluster_001", "ping": 31}]}
        },
        {
            "desc": "id + ping + 基础算力字段",
            "data": {"clusters": [{
                "id": "cluster_001",
                "ping": 31,
                "total_flops": 1464,
                "used_flops": 413,
                "available_flops": 1051
            }]}
        },
        {
            "desc": "所有必需字段（根据API文档）",
            "data": {"clusters": [{
                "id": "cluster_001",
                "ping": 31,
                "total_flops": 1464,
                "used_flops": 413,
                "available_flops": 1051,
                "total_cpu_cores": 363,
                "used_cpu_cores": 166,
                "available_cpu_cores": 197,
                "total_mem_size": 2625,
                "used_mem_size": 576,
                "available_mem_size": 2049,
                "total_gpu_cards": 80,
                "used_gpu_cards": 13,
                "available_gpu_cards": 67,
                "total_gpu_mem_size": 6400,
                "used_gpu_mem_size": 2570,
                "available_gpu_mem_size": 3830,
                "total_storage_size": 56956,
                "used_storage_size": 21633,
                "available_storage_size": 35323
            }]}
        }
    ]
    
    for test_case in test_cases:
        success = test_api_call(test_case["data"], test_case["desc"])
        print()
        if success:
            return test_case["data"]
    
    return None

def test_data_value_ranges():
    """测试数据值范围"""
    print("🔍 测试数据值范围")
    
    # 测试不同的数值范围
    test_cases = [
        {
            "desc": "小数值",
            "data": {"clusters": [{
                "id": "cluster_001",
                "ping": 1,
                "total_flops": 1,
                "used_flops": 0,
                "available_flops": 1,
                "total_cpu_cores": 1,
                "used_cpu_cores": 0,
                "available_cpu_cores": 1,
                "total_mem_size": 1,
                "used_mem_size": 0,
                "available_mem_size": 1,
                "total_gpu_cards": 1,
                "used_gpu_cards": 0,
                "available_gpu_cards": 1,
                "total_gpu_mem_size": 1,
                "used_gpu_mem_size": 0,
                "available_gpu_mem_size": 1,
                "total_storage_size": 1,
                "used_storage_size": 0,
                "available_storage_size": 1
            }]}
        },
        {
            "desc": "中等数值",
            "data": {"clusters": [{
                "id": "cluster_001",
                "ping": 10,
                "total_flops": 100,
                "used_flops": 10,
                "available_flops": 90,
                "total_cpu_cores": 100,
                "used_cpu_cores": 10,
                "available_cpu_cores": 90,
                "total_mem_size": 100,
                "used_mem_size": 10,
                "available_mem_size": 90,
                "total_gpu_cards": 10,
                "used_gpu_cards": 1,
                "available_gpu_cards": 9,
                "total_gpu_mem_size": 100,
                "used_gpu_mem_size": 10,
                "available_gpu_mem_size": 90,
                "total_storage_size": 1000,
                "used_storage_size": 100,
                "available_storage_size": 900
            }]}
        },
        {
            "desc": "API文档示例数值",
            "data": {"clusters": [{
                "id": "23456",
                "ping": 10,
                "total_flops": 29600,
                "used_flops": 0,
                "available_flops": 29600,
                "total_cpu_cores": 2038,
                "used_cpu_cores": 0,
                "available_cpu_cores": 2038,
                "total_mem_size": 12000,
                "used_mem_size": 0,
                "available_mem_size": 12000,
                "total_gpu_cards": 80,
                "used_gpu_cards": 0,
                "available_gpu_cards": 80,
                "total_gpu_mem_size": 6000,
                "used_gpu_mem_size": 0,
                "available_gpu_mem_size": 6000,
                "total_storage_size": 50000,
                "used_storage_size": 10000,
                "available_storage_size": 40000
            }]}
        }
    ]
    
    for test_case in test_cases:
        success = test_api_call(test_case["data"], test_case["desc"])
        print()
        if success:
            return test_case["data"]
    
    return None

def test_id_formats():
    """测试不同的ID格式"""
    print("🔍 测试不同的ID格式")
    
    test_cases = [
        {
            "desc": "纯数字ID",
            "data": {"clusters": [{"id": "12345", "ping": 10}]}
        },
        {
            "desc": "短ID",
            "data": {"clusters": [{"id": "test", "ping": 10}]}
        },
        {
            "desc": "下划线ID",
            "data": {"clusters": [{"id": "cluster_001", "ping": 10}]}
        },
        {
            "desc": "连字符ID",
            "data": {"clusters": [{"id": "cluster-001", "ping": 10}]}
        }
    ]
    
    for test_case in test_cases:
        success = test_api_call(test_case["data"], test_case["desc"])
        print()
        if success:
            return test_case["data"]
    
    return None

def main():
    """主函数"""
    print("🔍 调试数据格式问题")
    print("=" * 60)
    
    # 首先确认空数组仍然成功
    print("=== 确认空数组仍然成功 ===")
    test_api_call({"clusters": []}, "空数组")
    print()
    
    # 测试最小必需字段
    valid_data = test_minimal_required_fields()
    
    if not valid_data:
        # 测试数据值范围
        valid_data = test_data_value_ranges()
    
    if not valid_data:
        # 测试ID格式
        valid_data = test_id_formats()
    
    print("=" * 60)
    if valid_data:
        print("🎉 找到有效的数据格式!")
        print("有效数据:")
        print(json.dumps(valid_data, indent=2, ensure_ascii=False))
        
        print("\n建议:")
        print("1. 使用找到的有效格式更新主程序")
        print("2. 重新发送数据")
    else:
        print("❌ 未找到有效的数据格式")
        print("\n可能的原因:")
        print("1. API权限限制 - 只允许读取，不允许写入")
        print("2. 数据验证规则过于严格")
        print("3. 需要特殊的请求头或参数")
        print("4. API版本不匹配")
        
        print("\n建议:")
        print("1. 联系API提供方确认数据格式要求")
        print("2. 确认API密钥的权限范围")
        print("3. 获取最新的API文档和示例")

if __name__ == "__main__":
    main()
