@echo off
chcp 65001 >nul
echo 🔧 设置Windows任务计划程序
echo ================================

set SCRIPT_DIR=%~dp0
set PYTHON_EXE=C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
set SCRIPT_PATH=%SCRIPT_DIR%cluster_data_updater.py

echo 当前目录: %SCRIPT_DIR%
echo Python路径: %PYTHON_EXE%
echo 脚本路径: %SCRIPT_PATH%

echo.
echo 正在创建任务计划...

:: 删除已存在的任务（如果有）
schtasks /delete /tn "TaoPower集群数据更新" /f >nul 2>&1

:: 创建新的任务计划
schtasks /create ^
    /tn "TaoPower集群数据更新" ^
    /tr "\"%PYTHON_EXE%\" \"%SCRIPT_PATH%\"" ^
    /sc onstart ^
    /ru SYSTEM ^
    /rl highest ^
    /f

if %errorlevel% equ 0 (
    echo ✅ 任务计划创建成功!
    echo.
    echo 📋 任务详情:
    echo   任务名称: TaoPower集群数据更新
    echo   触发条件: 系统启动时
    echo   运行账户: SYSTEM
    echo   权限级别: 最高
    echo.
    echo 🚀 立即启动任务...
    schtasks /run /tn "TaoPower集群数据更新"
    
    if %errorlevel% equ 0 (
        echo ✅ 任务启动成功!
        echo.
        echo 💡 提示:
        echo   - 程序现在会在系统启动时自动运行
        echo   - 即使用户注销也会继续运行
        echo   - 可以在"任务计划程序"中查看和管理
    ) else (
        echo ❌ 任务启动失败
    )
) else (
    echo ❌ 任务计划创建失败
    echo 请以管理员身份运行此脚本
)

echo.
echo 按任意键退出...
pause >nul
