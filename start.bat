@echo off
echo 启动 TaoPower API 集群数据更新程序...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖...
pip show pandas >nul 2>&1
if errorlevel 1 (
    echo 安装依赖...
    pip install -r requirements.txt
)

REM 检查Excel文件是否存在
if not exist "clusters.xlsx" (
    echo 警告: clusters.xlsx 文件不存在
    echo 请确保Excel文件存在并包含正确的数据格式
    pause
)

REM 启动程序
echo 启动程序...
python cluster_data_updater.py

pause
