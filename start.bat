@echo off
echo 启动 TaoPower API 集群数据更新程序...
echo.

REM 设置Python路径
set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe

REM 检查Python是否存在
if not exist "%PYTHON_PATH%" (
    echo 错误: 未找到Python，请检查Python安装路径
    echo 当前查找路径: %PYTHON_PATH%
    pause
    exit /b 1
)

echo 使用Python: %PYTHON_PATH%
"%PYTHON_PATH%" --version

REM 检查CSV文件是否存在
if not exist "clusters.csv" (
    echo 创建示例CSV文件...
    "%PYTHON_PATH%" create_sample_csv.py
)

REM 启动程序
echo 启动程序...
"%PYTHON_PATH%" cluster_data_updater.py

pause
