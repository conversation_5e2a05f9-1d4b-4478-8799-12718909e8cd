# TaoPower API 集群数据自动更新程序

## 项目概述

这是一个自动化程序，可以从Excel文件中读取集群数据，并按照时间顺序每天自动更新到TaoPower API。程序使用您提供的API密钥进行身份验证，支持HMAC-SHA256签名算法。

## 文件结构

```
MyProject/
├── cluster_data_updater.py    # 主程序文件
├── config.py                  # 配置文件
├── test_api.py               # API测试脚本
├── create_sample_excel.py    # 示例Excel文件生成器
├── requirements.txt          # Python依赖包
├── start.bat                 # Windows启动脚本
├── README.md                 # 英文说明文档
├── 项目说明.md               # 中文说明文档
├── clusters.xlsx             # 数据文件（需要您创建或生成）
└── cluster_updater.log       # 日志文件（程序运行时自动生成）
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 创建示例Excel文件（如果您还没有）

```bash
python create_sample_excel.py
```

这会创建一个包含30行示例数据的`clusters.xlsx`文件。

### 3. 测试API连接

```bash
python test_api.py
```

这会测试您的API密钥是否正确，网络连接是否正常。

### 4. 运行主程序

**方法一：使用批处理文件（Windows）**
```bash
start.bat
```

**方法二：直接运行Python**
```bash
python cluster_data_updater.py
```

## 程序功能

### 核心功能
- 📊 **Excel数据读取**: 从`clusters.xlsx`文件读取集群数据
- 🔄 **定时更新**: 每天在指定时间（默认上午9:00）自动更新数据
- 🔐 **安全认证**: 使用HMAC-SHA256签名算法确保API调用安全
- 📝 **日志记录**: 详细记录程序运行状态和API调用结果
- 🔁 **循环使用**: 数据读取完毕后自动从头开始

### 工作流程
1. 程序启动时加载Excel文件
2. 立即执行一次数据更新（用于测试）
3. 设置定时任务，每天在指定时间执行更新
4. 按行顺序读取Excel数据，每天更新一行
5. 到达文件末尾后重新从第一行开始

## 配置说明

### API配置（config.py）
```python
API_CONFIG = {
    "ACCESS_KEY": "您的Access Key",
    "SECRET_KEY": "您的Secret Key", 
    "BASE_URL": "https://api.shanghaiai.com"
}
```

### 调度配置
```python
SCHEDULE_CONFIG = {
    "UPDATE_TIME": "09:00",  # 每日更新时间
    "CHECK_INTERVAL": 60     # 检查间隔（秒）
}
```

## Excel文件格式

`clusters.xlsx`文件必须包含以下列（列名必须完全匹配）：

| 列名 | 说明 | 数据类型 |
|------|------|----------|
| id | 集群唯一标识符 | 字符串 |
| ping | 网络延迟（毫秒） | 整数 |
| total_flops | 总算力（TFLOPS） | 整数 |
| used_flops | 已用算力（TFLOPS） | 整数 |
| available_flops | 可用算力（TFLOPS） | 整数 |
| total_cpu_cores | 总CPU核数 | 整数 |
| used_cpu_cores | 已用CPU核数 | 整数 |
| available_cpu_cores | 可用CPU核数 | 整数 |
| total_mem_size | 总内存（GB） | 整数 |
| used_mem_size | 已用内存（GB） | 整数 |
| available_mem_size | 可用内存（GB） | 整数 |
| total_gpu_cards | 总GPU卡数 | 整数 |
| used_gpu_cards | 已用GPU卡数 | 整数 |
| available_gpu_cards | 可用GPU卡数 | 整数 |
| total_gpu_mem_size | 总GPU内存（GB） | 整数 |
| used_gpu_mem_size | 已用GPU内存（GB） | 整数 |
| available_gpu_mem_size | 可用GPU内存（GB） | 整数 |
| total_storage_size | 总存储（GB） | 整数 |
| used_storage_size | 已用存储（GB） | 整数 |
| available_storage_size | 可用存储（GB） | 整数 |

## 日志监控

程序会在`cluster_updater.log`文件中记录详细信息：
- 程序启动和停止
- Excel文件加载状态
- API调用成功/失败
- 错误信息和异常

## 常见问题

### Q: 程序启动后立即退出
A: 检查`clusters.xlsx`文件是否存在且格式正确

### Q: API调用失败
A: 运行`python test_api.py`检查网络连接和API密钥

### Q: Excel文件读取错误
A: 确保Excel文件包含所有必需的列，且数据类型正确

### Q: 如何修改更新时间
A: 编辑`config.py`文件中的`SCHEDULE_CONFIG['UPDATE_TIME']`

### Q: 如何停止程序
A: 在命令行中按`Ctrl+C`

## 技术支持

如果遇到问题，请检查：
1. Python版本（建议3.7+）
2. 依赖包是否正确安装
3. Excel文件格式是否正确
4. 网络连接是否正常
5. API密钥是否有效

程序已经配置了您提供的API密钥：
- Access Key: KMgUamC48v2F34MZvSBHJfGQ
- Secret Key: ByKU7q2ZaYC275kiHRc626pG9qNdXKUqy3k1xaA0bI9kN6pk
