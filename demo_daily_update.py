#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示每日自动更新功能
模拟程序在不同日期运行时的行为
"""

import pandas as pd
from datetime import datetime, timedelta
import logging
import json
from config import CSV_COLUMNS, FILE_CONFIG, API_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class MockClusterDataManager:
    """模拟集群数据管理器（用于演示）"""
    
    def __init__(self, data_file: str):
        self.data_file = data_file
        self.data_df = None
        self.date_column = None
        self.has_date_column = False
    
    def load_csv_data(self) -> bool:
        """加载CSV数据"""
        try:
            self.data_df = pd.read_csv(self.data_file)
            
            # 检查是否有日期列
            date_columns = ['日期', 'date', 'Date', 'DATE', 'update_date', 'time']
            for col in date_columns:
                if col in self.data_df.columns:
                    self.date_column = col
                    self.has_date_column = True
                    # 转换日期列为datetime格式
                    self.data_df[col] = pd.to_datetime(self.data_df[col], errors='coerce')
                    logging.info(f"发现日期列: {col}")
                    break
            
            logging.info(f"成功加载CSV文件，共{len(self.data_df)}行数据")
            return True
            
        except Exception as e:
            logging.error(f"加载CSV文件失败: {e}")
            return False
    
    def get_data_by_date(self, target_date):
        """根据指定日期获取数据"""
        if not self.has_date_column:
            return None
        
        # 将目标日期转换为date对象
        if isinstance(target_date, str):
            target_date = datetime.strptime(target_date, "%Y-%m-%d").date()
        elif hasattr(target_date, 'date'):
            target_date = target_date.date()
        
        # 查找目标日期的数据
        target_data = self.data_df[self.data_df[self.date_column].dt.date == target_date]
        
        if not target_data.empty:
            logging.info(f"找到目标日期({target_date})的数据，共{len(target_data)}行")
            cluster_data_list = []
            for _, row in target_data.iterrows():
                cluster_data = self._build_cluster_data(row)
                if cluster_data:
                    cluster_data_list.append(cluster_data)
            return cluster_data_list
        else:
            logging.warning(f"未找到目标日期({target_date})的数据")
            # 查找最近的日期
            valid_dates = self.data_df[self.date_column].dropna()
            if valid_dates.empty:
                return None
            
            date_diffs = abs(valid_dates.dt.date - target_date)
            nearest_date = valid_dates.iloc[date_diffs.argmin()].date()
            
            logging.info(f"使用最接近的日期: {nearest_date}")
            nearest_data = self.data_df[self.data_df[self.date_column].dt.date == nearest_date]
            
            cluster_data_list = []
            for _, row in nearest_data.iterrows():
                cluster_data = self._build_cluster_data(row)
                if cluster_data:
                    cluster_data_list.append(cluster_data)
            return cluster_data_list
    
    def _build_cluster_data(self, row):
        """构建集群数据"""
        try:
            cluster_data = {}
            for api_field, csv_column in CSV_COLUMNS.items():
                if api_field == "id":
                    cluster_data[api_field] = str(row.get(csv_column, f"cluster_unknown"))
                elif api_field == "ping":
                    cluster_data[api_field] = int(row.get(csv_column, 10))
                else:
                    cluster_data[api_field] = int(row.get(csv_column, 0))
            
            return cluster_data
        except Exception as e:
            logging.error(f"构建集群数据失败: {e}")
            return None

def simulate_daily_update(date_str):
    """模拟指定日期的每日更新"""
    print(f"\n{'='*60}")
    print(f"模拟日期: {date_str}")
    print(f"{'='*60}")
    
    # 创建数据管理器
    manager = MockClusterDataManager(FILE_CONFIG['DATA_FILE'])
    
    # 加载数据
    if not manager.load_csv_data():
        print("❌ 数据加载失败")
        return
    
    # 获取指定日期的数据
    cluster_data_list = manager.get_data_by_date(date_str)
    
    if not cluster_data_list:
        print("❌ 未获取到数据")
        return
    
    print(f"✅ 获取到 {len(cluster_data_list)} 条集群数据")
    
    # 显示要更新的数据
    for i, cluster_data in enumerate(cluster_data_list, 1):
        print(f"\n📊 集群数据 {i}:")
        print(f"   集群ID: {cluster_data['id']}")
        print(f"   Ping值: {cluster_data['ping']} ms")
        print(f"   总算力: {cluster_data['total_flops']} TFLOPS")
        print(f"   已用算力: {cluster_data['used_flops']} TFLOPS")
        print(f"   可用算力: {cluster_data['available_flops']} TFLOPS")
        print(f"   总GPU卡数: {cluster_data['total_gpu_cards']} 张")
        print(f"   已用GPU卡数: {cluster_data['used_gpu_cards']} 张")
        print(f"   可用GPU卡数: {cluster_data['available_gpu_cards']} 张")
    
    # 模拟API调用
    print(f"\n🔄 模拟API调用...")
    api_payload = {"clusters": cluster_data_list}
    print(f"📤 API请求数据大小: {len(json.dumps(api_payload))} 字节")
    print(f"🎯 目标API: {API_CONFIG['BASE_URL']}/powerapi/v1/clusters")
    print(f"✅ 模拟更新成功（实际环境中会发送到TaoPower API）")

def main():
    """主函数"""
    print("🚀 TaoPower API 每日自动更新演示")
    print("根据CSV文件中的日期，自动选择对应数据进行更新")
    
    # 演示不同日期的更新
    test_dates = [
        "2025-07-10",  # 今天
        "2025-07-11",  # 明天
        "2025-07-15",  # 5天后
        "2025-07-20",  # 10天后
        "2025-08-01",  # 下个月
    ]
    
    for date_str in test_dates:
        simulate_daily_update(date_str)
    
    print(f"\n{'='*60}")
    print("📋 演示总结:")
    print("✅ 程序能够根据当前日期自动选择对应的集群数据")
    print("✅ 如果当天没有数据，会自动选择最接近的日期")
    print("✅ 数据格式完全符合TaoPower API要求")
    print("✅ 支持每天定时自动更新（默认上午9:00）")
    print("✅ 详细的日志记录，便于监控和调试")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
