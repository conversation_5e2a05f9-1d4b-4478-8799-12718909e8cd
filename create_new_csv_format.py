#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
根据API文档创建新的CSV格式并测试
"""

import pandas as pd
import requests
import json
import hmac
import hashlib
import time
import urllib3
from config import API_CONFIG

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def create_devices_csv():
    """创建设备产品数据CSV"""
    print("=== 创建设备产品数据CSV ===")
    
    # 根据API文档创建设备数据
    devices_data = [
        {
            "日期": "2025-07-10",
            "id": "device_001",
            "price": 100,
            "price_unit": 2,  # 天
            "device_number": 50
        },
        {
            "日期": "2025-07-11", 
            "id": "device_002",
            "price": 200,
            "price_unit": 3,  # 月
            "device_number": 30
        }
    ]
    
    df = pd.DataFrame(devices_data)
    df.to_csv("devices.csv", index=False)
    print("✅ 设备数据CSV已创建: devices.csv")
    print(df.to_string(index=False))
    
    return "devices.csv"

def create_clusters_csv():
    """创建集群数据CSV（使用API文档示例）"""
    print("\n=== 创建集群数据CSV ===")
    
    # 使用API文档中的精确示例数据
    clusters_data = [
        {
            "日期": "2025-07-10",
            "id": "23456",
            "ping": 10,
            "total_flops": 29600,
            "used_flops": 0,
            "available_flops": 29600,
            "total_cpu_cores": 2038,
            "used_cpu_cores": 0,
            "available_cpu_cores": 2038,
            "total_mem_size": 12000,
            "used_mem_size": 0,
            "available_mem_size": 12000,
            "total_gpu_cards": 80,
            "used_gpu_cards": 0,
            "available_gpu_cards": 80,
            "total_gpu_mem_size": 6000,
            "used_gpu_mem_size": 0,
            "available_gpu_mem_size": 6000,
            "total_storage_size": 50000,
            "used_storage_size": 10000,
            "available_storage_size": 40000
        },
        {
            "日期": "2025-07-11",
            "id": "23457",
            "ping": 11,
            "total_flops": 2960,
            "used_flops": 0,
            "available_flops": 2960,
            "total_cpu_cores": 2038,
            "used_cpu_cores": 0,
            "available_cpu_cores": 2038,
            "total_mem_size": 12000,
            "used_mem_size": 0,
            "available_mem_size": 12000,
            "total_gpu_cards": 80,
            "used_gpu_cards": 0,
            "available_gpu_cards": 80,
            "total_gpu_mem_size": 6000,
            "used_gpu_mem_size": 0,
            "available_gpu_mem_size": 6000,
            "total_storage_size": 50000,
            "used_storage_size": 10000,
            "available_storage_size": 40000
        }
    ]
    
    df = pd.DataFrame(clusters_data)
    df.to_csv("clusters_new.csv", index=False)
    print("✅ 集群数据CSV已创建: clusters_new.csv")
    print(df.head().to_string(index=False))
    
    return "clusters_new.csv"

def test_devices_api():
    """测试设备API"""
    print("\n=== 测试设备API ===")
    
    # 读取设备数据
    df = pd.read_csv("devices.csv")
    today = pd.Timestamp.now().date()
    
    # 获取今天的数据
    df['日期'] = pd.to_datetime(df['日期'], errors='coerce')
    today_data = df[df['日期'].dt.date == today]
    
    if not today_data.empty:
        row = today_data.iloc[0]
        print(f"✅ 使用今天({today})的设备数据")
    else:
        row = df.iloc[0]
        print(f"✅ 使用第一行设备数据")
    
    # 构建设备数据
    device_data = {
        "id": str(row["id"]),
        "price": int(row["price"]),
        "price_unit": int(row["price_unit"]),
        "device_number": int(row["device_number"])
    }
    
    print(f"设备数据: {device_data}")
    
    # 构建API请求
    api_data = {"devices": [device_data]}
    body = json.dumps(api_data, separators=(',', ':'))
    
    # 生成签名
    ts = int(time.time() * 1000)
    access_key = API_CONFIG['ACCESS_KEY']
    secret_key = API_CONFIG['SECRET_KEY']
    
    to_sign = f"{ts}{access_key}{body}"
    signature = hmac.new(
        secret_key.encode('utf-8'),
        to_sign.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    headers = {
        "Content-Type": "application/json",
        "ts": str(ts),
        "accessKey": access_key,
        "sign": signature
    }
    
    url = f"{API_CONFIG['BASE_URL']}/powerapi/v1/devices"
    cert_file = "上海市算力调度平台（悦科数据）.crt"
    key_file = "上海市算力调度平台（悦科数据）.key"
    
    print(f"请求URL: {url}")
    print(f"请求数据: {body}")
    
    try:
        response = requests.post(
            url,
            headers=headers,
            data=body,
            timeout=30,
            cert=(cert_file, key_file),
            verify=False
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("🎉 设备API调用成功!")
                return True
            else:
                print(f"❌ 设备API返回错误: {result}")
        
    except Exception as e:
        print(f"❌ 设备API请求失败: {e}")
    
    return False

def test_clusters_api():
    """测试集群API"""
    print("\n=== 测试集群API ===")
    
    # 读取集群数据
    df = pd.read_csv("clusters_new.csv")
    today = pd.Timestamp.now().date()
    
    # 获取今天的数据
    df['日期'] = pd.to_datetime(df['日期'], errors='coerce')
    today_data = df[df['日期'].dt.date == today]
    
    if not today_data.empty:
        row = today_data.iloc[0]
        print(f"✅ 使用今天({today})的集群数据")
    else:
        row = df.iloc[0]
        print(f"✅ 使用第一行集群数据")
    
    # 构建集群数据
    cluster_data = {
        "id": str(row["id"]),
        "ping": int(row["ping"]),
        "total_flops": int(row["total_flops"]),
        "used_flops": int(row["used_flops"]),
        "available_flops": int(row["available_flops"]),
        "total_cpu_cores": int(row["total_cpu_cores"]),
        "used_cpu_cores": int(row["used_cpu_cores"]),
        "available_cpu_cores": int(row["available_cpu_cores"]),
        "total_mem_size": int(row["total_mem_size"]),
        "used_mem_size": int(row["used_mem_size"]),
        "available_mem_size": int(row["available_mem_size"]),
        "total_gpu_cards": int(row["total_gpu_cards"]),
        "used_gpu_cards": int(row["used_gpu_cards"]),
        "available_gpu_cards": int(row["available_gpu_cards"]),
        "total_gpu_mem_size": int(row["total_gpu_mem_size"]),
        "used_gpu_mem_size": int(row["used_gpu_mem_size"]),
        "available_gpu_mem_size": int(row["available_gpu_mem_size"]),
        "total_storage_size": int(row["total_storage_size"]),
        "used_storage_size": int(row["used_storage_size"]),
        "available_storage_size": int(row["available_storage_size"])
    }
    
    print(f"集群数据: ID={cluster_data['id']}, 总算力={cluster_data['total_flops']} TFLOPS")
    
    # 构建API请求
    api_data = {"clusters": [cluster_data]}
    body = json.dumps(api_data, separators=(',', ':'))
    
    # 生成签名
    ts = int(time.time() * 1000)
    access_key = API_CONFIG['ACCESS_KEY']
    secret_key = API_CONFIG['SECRET_KEY']
    
    to_sign = f"{ts}{access_key}{body}"
    signature = hmac.new(
        secret_key.encode('utf-8'),
        to_sign.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    headers = {
        "Content-Type": "application/json",
        "ts": str(ts),
        "accessKey": access_key,
        "sign": signature
    }
    
    url = f"{API_CONFIG['BASE_URL']}/powerapi/v1/clusters"
    cert_file = "上海市算力调度平台（悦科数据）.crt"
    key_file = "上海市算力调度平台（悦科数据）.key"
    
    print(f"请求URL: {url}")
    print(f"请求数据: {body}")
    
    try:
        response = requests.post(
            url,
            headers=headers,
            data=body,
            timeout=30,
            cert=(cert_file, key_file),
            verify=False
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("🎉 集群API调用成功!")
                return True
            else:
                print(f"❌ 集群API返回错误: {result}")
        
    except Exception as e:
        print(f"❌ 集群API请求失败: {e}")
    
    return False

def main():
    """主函数"""
    print("🔧 根据API文档创建新的数据格式")
    print("=" * 60)
    
    # 创建新的CSV文件
    devices_file = create_devices_csv()
    clusters_file = create_clusters_csv()
    
    # 测试两个API端点
    devices_success = test_devices_api()
    clusters_success = test_clusters_api()
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    
    if devices_success:
        print("✅ 设备API调用成功!")
    else:
        print("❌ 设备API调用失败")
    
    if clusters_success:
        print("✅ 集群API调用成功!")
    else:
        print("❌ 集群API调用失败")
    
    if devices_success or clusters_success:
        print("\n🎉 找到可用的API端点!")
        print("建议:")
        if devices_success:
            print("1. 使用设备API端点进行数据上传")
            print("2. 更新主程序使用devices.csv和/powerapi/v1/devices端点")
        if clusters_success:
            print("1. 使用集群API端点进行数据上传")
            print("2. 更新主程序使用clusters_new.csv和/powerapi/v1/clusters端点")
    else:
        print("\n❌ 两个API端点都无法成功调用")
        print("可能需要:")
        print("1. 确认API密钥权限")
        print("2. 联系API提供方确认数据格式")
        print("3. 检查证书权限设置")

if __name__ == "__main__":
    main()
