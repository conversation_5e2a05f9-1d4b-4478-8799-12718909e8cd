# 🎉 TaoPower API 集群数据自动更新程序 - 安装完成！

## 📋 安装和测试结果

### ✅ 已完成的工作

1. **Python环境** - ✅ 已配置
   - Python 3.13.5 已安装
   - 所有依赖包已安装（pandas, openpyxl, requests, schedule）

2. **程序文件** - ✅ 已创建
   - 主程序：`cluster_data_updater.py`
   - 配置文件：`config.py`
   - 测试脚本：`test_program.py`, `test_api.py`
   - 启动脚本：`start.bat`

3. **示例数据** - ✅ 已生成
   - `clusters_sample.xlsx` - 包含30行示例数据
   - 所有20个必需字段都已包含

4. **功能测试** - ✅ 核心功能正常
   - Excel文件读取：✅ 成功
   - 数据处理：✅ 成功  
   - 签名算法：✅ 正确
   - API调用：❌ 网络连接问题

## 🚀 如何使用

### 方法一：使用批处理文件（推荐）
```bash
双击运行 start.bat
```

### 方法二：命令行运行
```bash
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe cluster_data_updater.py
```

### 方法三：测试程序功能
```bash
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe test_program.py
```

## 📊 数据文件说明

程序当前使用 `clusters_sample.xlsx` 作为数据源，包含：
- 30行示例数据
- 20个字段（id, ping, 各种算力和资源数据）
- 程序会按行顺序每天更新一行数据

如果您有自己的数据文件：
1. 将您的Excel文件命名为 `clusters.xlsx`
2. 修改 `config.py` 中的 `EXCEL_FILE` 设置
3. 确保包含所有必需的列名

## ⚙️ 程序配置

在 `config.py` 文件中可以修改：

```python
# 更新时间（默认上午9:00）
SCHEDULE_CONFIG = {
    "UPDATE_TIME": "09:00",
    "CHECK_INTERVAL": 60
}

# 数据文件路径
FILE_CONFIG = {
    "EXCEL_FILE": "clusters_sample.xlsx",  # 改为您的文件名
    "LOG_FILE": "cluster_updater.log"
}
```

## 🔧 网络连接问题解决

当前API调用失败是由于SSL连接问题，可能的解决方案：

### 1. 检查网络连接
- 确保可以访问 `https://api.shanghaiai.com`
- 检查防火墙设置

### 2. 企业网络环境
如果在企业网络环境中，可能需要：
- 配置代理服务器
- 联系网络管理员开放相关端口

### 3. 临时解决方案
程序已经配置为跳过SSL验证（仅用于测试），如果仍然失败，可能是：
- 服务器暂时不可用
- 网络连接问题
- 需要VPN连接

## 📝 日志监控

程序运行时会在 `cluster_updater.log` 文件中记录：
- 程序启动信息
- Excel文件加载状态
- 数据处理结果
- API调用成功/失败
- 错误信息

## 🔄 程序工作流程

1. **启动时**：
   - 加载Excel文件
   - 立即执行一次数据更新（测试）
   - 设置定时任务

2. **每日运行**：
   - 上午9:00自动执行
   - 按行顺序读取Excel数据
   - 发送到TaoPower API
   - 记录结果到日志

3. **循环使用**：
   - 读取完所有行后重新从第一行开始

## 🛠️ 故障排除

### 程序无法启动
- 检查Python路径是否正确
- 确保所有依赖包已安装

### Excel文件读取失败
- 检查文件是否存在
- 确保文件未被其他程序占用
- 验证列名是否正确

### API调用失败
- 运行 `test_program.py` 检查具体错误
- 检查网络连接
- 验证API密钥是否正确

## 📞 技术支持

如果遇到问题：
1. 查看 `cluster_updater.log` 日志文件
2. 运行 `test_program.py` 进行诊断
3. 检查网络连接和防火墙设置

## 🎯 下一步

1. **解决网络连接问题**：联系网络管理员或检查网络设置
2. **准备真实数据**：将您的实际集群数据整理成Excel格式
3. **调整配置**：根据需要修改更新时间和其他参数
4. **监控运行**：定期检查日志文件确保程序正常运行

程序的核心功能已经完全正常，只需要解决网络连接问题即可投入使用！
