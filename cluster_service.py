#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows服务包装器 - 将集群数据更新程序作为Windows服务运行
"""

import sys
import os
import time
import logging
import servicemanager
import win32event
import win32service
import win32serviceutil
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# 导入主程序
from cluster_data_updater import ClusterDataUpdater

class ClusterDataService(win32serviceutil.ServiceFramework):
    """集群数据更新Windows服务"""
    
    _svc_name_ = "ClusterDataUpdater"
    _svc_display_name_ = "TaoPower集群数据自动更新服务"
    _svc_description_ = "自动上传集群数据到TaoPower API的Windows服务"
    
    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.is_alive = True
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置服务日志"""
        log_file = current_dir / "service.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def SvcStop(self):
        """停止服务"""
        self.logger.info("正在停止集群数据更新服务...")
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.is_alive = False
        
    def SvcDoRun(self):
        """运行服务"""
        self.logger.info("启动集群数据更新服务...")
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STARTED,
            (self._svc_name_, '')
        )
        
        try:
            # 初始化主程序
            updater = ClusterDataUpdater()
            self.logger.info("集群数据更新服务已启动")
            
            # 服务主循环
            while self.is_alive:
                # 检查是否收到停止信号
                rc = win32event.WaitForSingleObject(self.hWaitStop, 1000)
                if rc == win32event.WAIT_OBJECT_0:
                    break
                
                # 让主程序运行
                time.sleep(1)
                
        except Exception as e:
            self.logger.error(f"服务运行错误: {e}")
            servicemanager.LogErrorMsg(f"服务运行错误: {e}")
        
        self.logger.info("集群数据更新服务已停止")

def install_service():
    """安装服务"""
    try:
        win32serviceutil.InstallService(
            ClusterDataService._svc_reg_class_,
            ClusterDataService._svc_name_,
            ClusterDataService._svc_display_name_,
            description=ClusterDataService._svc_description_,
            startType=win32service.SERVICE_AUTO_START
        )
        print("✅ 服务安装成功!")
        print(f"服务名称: {ClusterDataService._svc_name_}")
        print(f"显示名称: {ClusterDataService._svc_display_name_}")
        print("服务将在系统启动时自动启动")
    except Exception as e:
        print(f"❌ 服务安装失败: {e}")

def uninstall_service():
    """卸载服务"""
    try:
        win32serviceutil.RemoveService(ClusterDataService._svc_name_)
        print("✅ 服务卸载成功!")
    except Exception as e:
        print(f"❌ 服务卸载失败: {e}")

def start_service():
    """启动服务"""
    try:
        win32serviceutil.StartService(ClusterDataService._svc_name_)
        print("✅ 服务启动成功!")
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")

def stop_service():
    """停止服务"""
    try:
        win32serviceutil.StopService(ClusterDataService._svc_name_)
        print("✅ 服务停止成功!")
    except Exception as e:
        print(f"❌ 服务停止失败: {e}")

def main():
    """主函数"""
    if len(sys.argv) == 1:
        # 作为服务运行
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(ClusterDataService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        # 命令行操作
        command = sys.argv[1].lower()
        
        if command == 'install':
            install_service()
        elif command == 'uninstall':
            uninstall_service()
        elif command == 'start':
            start_service()
        elif command == 'stop':
            stop_service()
        elif command == 'restart':
            stop_service()
            time.sleep(2)
            start_service()
        else:
            print("使用方法:")
            print("  python cluster_service.py install   - 安装服务")
            print("  python cluster_service.py uninstall - 卸载服务")
            print("  python cluster_service.py start     - 启动服务")
            print("  python cluster_service.py stop      - 停止服务")
            print("  python cluster_service.py restart   - 重启服务")

if __name__ == '__main__':
    main()
