@echo off
chcp 65001 >nul
echo 🚀 启动集群数据更新程序（后台模式）
echo ========================================

set SCRIPT_DIR=%~dp0
set PYTHON_EXE=C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
set SCRIPT_PATH=%SCRIPT_DIR%cluster_data_updater.py

echo 当前目录: %SCRIPT_DIR%
echo Python路径: %PYTHON_EXE%
echo 脚本路径: %SCRIPT_PATH%

:: 检查程序是否已在运行
tasklist /fi "imagename eq python.exe" /fi "windowtitle eq cluster_data_updater*" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  程序可能已在运行中
    echo 正在检查进程...
    tasklist /fi "imagename eq python.exe" | findstr python.exe
    echo.
    choice /c YN /m "是否强制重启程序? (Y/N)"
    if errorlevel 2 goto :end
    
    echo 正在停止现有进程...
    taskkill /f /im python.exe /fi "windowtitle eq cluster_data_updater*" >nul 2>&1
    timeout /t 2 >nul
)

echo.
echo 🔄 启动程序...

:: 使用start命令在后台启动，不显示窗口
start /b /min "" "%PYTHON_EXE%" "%SCRIPT_PATH%"

:: 等待程序启动
timeout /t 3 >nul

:: 检查程序是否成功启动
tasklist /fi "imagename eq python.exe" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 程序启动成功!
    echo.
    echo 📋 程序状态:
    echo   - 运行模式: 后台运行
    echo   - 窗口状态: 最小化/隐藏
    echo   - 自动更新: 每日09:00
    echo.
    echo 💡 管理提示:
    echo   - 查看日志: cluster_updater.log
    echo   - 停止程序: stop_program.bat
    echo   - 重启程序: 重新运行此脚本
    echo.
    echo 🔍 当前Python进程:
    tasklist /fi "imagename eq python.exe"
) else (
    echo ❌ 程序启动失败
    echo 请检查Python路径和脚本文件是否正确
)

:end
echo.
echo 按任意键退出...
pause >nul
