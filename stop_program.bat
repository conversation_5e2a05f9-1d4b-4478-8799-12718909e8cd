@echo off
chcp 65001 >nul
echo 🛑 停止集群数据更新程序
echo ========================

echo 正在查找运行中的程序...

:: 查找Python进程
tasklist /fi "imagename eq python.exe" | findstr python.exe >nul
if %errorlevel% neq 0 (
    echo ℹ️  没有找到运行中的Python程序
    goto :end
)

echo.
echo 🔍 当前Python进程:
tasklist /fi "imagename eq python.exe"

echo.
choice /c YN /m "确认停止所有Python进程? (Y/N)"
if errorlevel 2 goto :end

echo.
echo 🔄 正在停止程序...

:: 强制停止Python进程
taskkill /f /im python.exe >nul 2>&1

if %errorlevel% equ 0 (
    echo ✅ 程序已停止
) else (
    echo ⚠️  停止过程中可能出现问题
)

:: 再次检查
timeout /t 2 >nul
tasklist /fi "imagename eq python.exe" | findstr python.exe >nul
if %errorlevel% neq 0 (
    echo ✅ 确认所有Python进程已停止
) else (
    echo ⚠️  仍有Python进程在运行:
    tasklist /fi "imagename eq python.exe"
)

:end
echo.
echo 按任意键退出...
pause >nul
