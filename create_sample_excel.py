#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建示例Excel文件
生成一个包含示例数据的clusters.xlsx文件
"""

import pandas as pd
import random
from datetime import datetime

def create_sample_data(num_rows=30):
    """创建示例数据"""
    data = []
    
    for i in range(num_rows):
        # 生成随机但合理的集群数据
        total_flops = random.randint(1000, 50000)
        used_flops = random.randint(0, total_flops // 2)
        available_flops = total_flops - used_flops
        
        total_cpu_cores = random.randint(64, 2048)
        used_cpu_cores = random.randint(0, total_cpu_cores // 2)
        available_cpu_cores = total_cpu_cores - used_cpu_cores
        
        total_mem_size = random.randint(512, 12000)
        used_mem_size = random.randint(0, total_mem_size // 2)
        available_mem_size = total_mem_size - used_mem_size
        
        total_gpu_cards = random.randint(8, 100)
        used_gpu_cards = random.randint(0, total_gpu_cards // 2)
        available_gpu_cards = total_gpu_cards - used_gpu_cards
        
        total_gpu_mem_size = total_gpu_cards * random.randint(40, 80)
        used_gpu_mem_size = random.randint(0, total_gpu_mem_size // 2)
        available_gpu_mem_size = total_gpu_mem_size - used_gpu_mem_size
        
        total_storage_size = random.randint(10000, 100000)
        used_storage_size = random.randint(1000, total_storage_size // 2)
        available_storage_size = total_storage_size - used_storage_size
        
        row_data = {
            "id": f"cluster_{i+1:03d}",
            "ping": random.randint(5, 50),
            "total_flops": total_flops,
            "used_flops": used_flops,
            "available_flops": available_flops,
            "total_cpu_cores": total_cpu_cores,
            "used_cpu_cores": used_cpu_cores,
            "available_cpu_cores": available_cpu_cores,
            "total_mem_size": total_mem_size,
            "used_mem_size": used_mem_size,
            "available_mem_size": available_mem_size,
            "total_gpu_cards": total_gpu_cards,
            "used_gpu_cards": used_gpu_cards,
            "available_gpu_cards": available_gpu_cards,
            "total_gpu_mem_size": total_gpu_mem_size,
            "used_gpu_mem_size": used_gpu_mem_size,
            "available_gpu_mem_size": available_gpu_mem_size,
            "total_storage_size": total_storage_size,
            "used_storage_size": used_storage_size,
            "available_storage_size": available_storage_size
        }
        
        data.append(row_data)
    
    return data

def main():
    """主函数"""
    print("创建示例Excel文件...")
    
    # 生成示例数据
    sample_data = create_sample_data(30)  # 生成30行数据
    
    # 创建DataFrame
    df = pd.DataFrame(sample_data)
    
    # 保存到Excel文件
    filename = "clusters_sample.xlsx"
    df.to_excel(filename, index=False, engine='openpyxl')
    
    print(f"✅ 示例Excel文件已创建: {filename}")
    print(f"📊 包含 {len(sample_data)} 行数据")
    print("\n文件包含以下列:")
    for i, col in enumerate(df.columns, 1):
        print(f"{i:2d}. {col}")
    
    print(f"\n前5行数据预览:")
    print(df.head().to_string(index=False))
    
    print(f"\n💡 提示:")
    print(f"- 您可以编辑 {filename} 文件来修改数据")
    print(f"- 程序会按行顺序每天更新一行数据")
    print(f"- 到达文件末尾后会重新从第一行开始")

if __name__ == "__main__":
    main()
